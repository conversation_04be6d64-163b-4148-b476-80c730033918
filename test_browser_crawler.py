#!/usr/bin/env python3
"""
Test script for Browser Shortcode Crawler

This script tests the browser-based crawler functionality to ensure
<PERSON><PERSON> is properly installed and working.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the package to Python path
sys.path.insert(0, str(Path(__file__).parent))

from amazon_affiliate_integration.crawlers.browser_shortcode_crawler import BrowserShortcodeCrawler
from amazon_affiliate_integration.core.models import ArticleInfo

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_browser_crawler():
    """Test the browser crawler with a sample URL"""
    logger = logging.getLogger(__name__)
    
    # Test article (you can replace with your actual domain)
    test_articles = [
        ArticleInfo(
            id=1,
            title="Test Article",
            url="https://example.com",  # Replace with actual URL
            content="",
            date="2025-01-01",
            modified="2025-01-01",
            slug="test-article",
            domain="example.com"
        )
    ]
    
    logger.info("Testing Browser Shortcode Crawler...")
    
    try:
        async with BrowserShortcodeCrawler(concurrency_limit=1) as crawler:
            logger.info("✓ Browser crawler initialized successfully")
            
            results = await crawler.crawl_articles(test_articles)
            
            logger.info(f"✓ Crawling completed. Results: {len(results)}")
            
            for result in results:
                logger.info(f"URL: {result.url}")
                logger.info(f"Status: {result.status}")
                logger.info(f"Success: {result.success}")
                logger.info(f"Shortcodes found: {result.total_shortcodes_found}")
                if result.error_message:
                    logger.info(f"Error: {result.error_message}")
                logger.info("---")
            
            return True
            
    except Exception as e:
        logger.error(f"✗ Browser crawler test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger = logging.getLogger(__name__)
    
    logger.info("=== Browser Crawler Test ===")
    
    success = await test_browser_crawler()
    
    if success:
        logger.info("✅ All tests passed! Browser crawler is working.")
        return 0
    else:
        logger.error("❌ Tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
