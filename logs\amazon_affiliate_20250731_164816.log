2025-07-31 16:48:16,468 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250731_164816.log
2025-07-31 16:48:16,469 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250731_164816.log
2025-07-31 16:48:16,469 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-07-31 16:48:16,470 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-07-31 16:48:16,470 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-07-31 16:48:16,480 - __main__ - INFO - Starting complete workflow: Process → Crawl → Cleanup...
2025-07-31 16:48:16,481 - amazon_affiliate_integration - INFO - 🚀 Starting COMPLETE workflow: Process → Crawl → Cleanup
2025-07-31 16:48:16,481 - amazon_affiliate_integration - INFO - 📝 Phase 1: Processing articles to add Amazon shortcodes...
2025-07-31 16:48:16,481 - amazon_affiliate_integration - INFO - Starting domain processing: decorupbeat.com (force=False, dry_run=False, limit=5)
2025-07-31 16:48:16,481 - amazon_affiliate_integration - INFO - Loaded 0 previously processed URLs
2025-07-31 16:48:16,482 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-07-31 16:48:16,482 - amazon_affiliate_integration - ERROR - Error processing domain decorupbeat.com: ProcessingState.__init__() got an unexpected keyword argument 'force_mode'
2025-07-31 16:48:16,482 - amazon_affiliate_integration - ERROR - ❌ Phase 1 failed - aborting complete workflow
2025-07-31 16:48:16,482 - __main__ - ERROR - Complete workflow failed
