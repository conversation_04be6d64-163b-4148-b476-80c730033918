"""
Amazon Affiliate Integration Script

This script enhances existing WordPress articles with Amazon affiliate products using AAWP shortcodes.
It fetches articles published from January 2025 onwards, analyzes H2 headings and their second paragraphs
using OpenAI to identify the 2 most expensive products, and inserts AAWP shortcodes after the first
paragraph of each H2 section.

Features:
- Fetches WordPress articles by publication date
- Preserves Gutenberg block format
- Creates comprehensive backups before modifications
- Uses OpenAI for intelligent product analysis
- Inserts AAWP shortcodes in correct positions
- Provides restore functionality
- Comprehensive error handling and logging

Usage:
    python 8_Amazon_Affiliate_Integration.py [--domain DOMAIN] [--dry-run] [--restore POST_ID]
"""

import asyncio
import json
import logging
import os
import re
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import httpx
import aiofiles
from dotenv import load_dotenv

# Load environment variables with interpolation
load_dotenv(interpolate=True)

# Configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
AAWP_TRACKING_ID = os.getenv('AAWP_TRACKING_ID', 'piatto-20')
AFFILIATE_BACKUP_DIR = Path(os.getenv('AFFILIATE_BACKUP_DIR', 'amazon_affiliate_backups'))

# WordPress credentials mapping
WP_CREDENTIALS = {
    'plumbingreads.com': os.getenv('PLUMBINGREADS_COM_WP_CREDENTIALS'),
    'majesticmoods.com': os.getenv('MAJESTICMOODS_COM_WP_CREDENTIALS'),
    'interiornook.com': os.getenv('INTERIORNOOK_COM_WP_CREDENTIALS'),
    'decorupbeat.com': os.getenv('DECORUPBEAT_COM_WP_CREDENTIALS'),
    'polisheddecor.com': os.getenv('POLISHEDDECOR_COM_WP_CREDENTIALS'),
    'lavisliving.com': os.getenv('LAVISLIVING_COM_WP_CREDENTIALS'),
    'cozytones.com': os.getenv('COZYTONES_COM_WP_CREDENTIALS'),
    'showerredefined.com': os.getenv('SHOWERREDEFINED_COM_WP_CREDENTIALS'),
    'modernistinterior.com': os.getenv('MODERNISTINTERIOR_COM_WP_CREDENTIALS'),
    'cozycues.com': os.getenv('COZYCUES_COM_WP_CREDENTIALS'),
}

# Logging setup
def setup_logging():
    """Set up comprehensive logging"""
    log_dir = Path('logs')
    log_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = log_dir / f'amazon_affiliate_integration_{timestamp}.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

class OpenAIClient:
    """OpenAI API client for product analysis"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.openai.com/v1"
        self.logger = logging.getLogger(__name__)
    
    async def analyze_products(self, heading: str, paragraph: str) -> List[str]:
        """
        Analyze heading and paragraph to identify 2 most expensive products
        
        Args:
            heading: H2 heading text
            paragraph: Second paragraph content
            
        Returns:
            List of 2 product names
        """
        prompt = f"""You are a product extraction specialist. Your task is to identify the 2 most expensive purchasable products for the specific application described in the heading and paragraph.

HEADING: {heading}
PARAGRAPH: {paragraph}

Requirements:
1. Focus on the MOST EXPENSIVE products that would be relevant to this specific application
2. Products should be actual purchasable items (not concepts or services)
3. Be specific with product names (include materials, brands, or premium features when relevant)
4. Prioritize luxury, premium, or high-end versions
5. Return exactly 2 products

Format your response as:
Product-1: [Specific expensive product name]
Product-2: [Specific expensive product name]

Examples of good responses:
Product-1: Premium Solid Wood Murphy Bed with Built-in Storage
Product-2: High-End Wall-Mounted Folding Desk with Leather Finish

Focus on expensive, specific, purchasable products only."""

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'model': 'gpt-4.1-nano',
            'messages': [
                {'role': 'system', 'content': 'You are a product extraction specialist focused on identifying expensive, purchasable products.'},
                {'role': 'user', 'content': prompt}
            ],
            'max_tokens': 150,
            'temperature': 0.7
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                
                # Parse the response to extract product names
                products = []
                for line in content.split('\n'):
                    if line.startswith('Product-'):
                        product_name = line.split(':', 1)[1].strip()
                        products.append(product_name)
                
                if len(products) >= 2:
                    return products[:2]
                else:
                    self.logger.warning(f"OpenAI returned fewer than 2 products: {content}")
                    return products  # Return whatever products we got, even if fewer than 2

        except Exception as e:
            self.logger.error(f"OpenAI API error: {e}")
            return []  # Return empty list instead of fallback products

class WordPressClient:
    """WordPress API client for article management"""
    
    def __init__(self, domain: str, credentials: str):
        self.domain = domain
        self.logger = logging.getLogger(__name__)
        
        # Parse credentials: site_url|username|password
        parts = credentials.split('|')
        if len(parts) != 3:
            raise ValueError(f"Invalid credentials format for {domain}")
        
        self.site_url = parts[0].rstrip('/')
        self.username = parts[1]
        self.password = parts[2]
        self.base_url = f"{self.site_url}/wp-json/wp/v2"
        
        # Setup authentication
        self.auth = httpx.BasicAuth(self.username, self.password)
    
    async def fetch_articles_since_january_2025(self) -> List[Dict[str, Any]]:
        """
        Fetch all articles published since January 1, 2025
        
        Returns:
            List of article dictionaries with id, title, content, etc.
        """
        articles = []
        page = 1
        per_page = 100
        
        # January 1, 2025 in ISO format
        after_date = "2025-01-01T00:00:00"
        
        try:
            async with httpx.AsyncClient(auth=self.auth, timeout=30.0) as client:
                while True:
                    params = {
                        'page': page,
                        'per_page': per_page,
                        'after': after_date,
                        'status': 'publish',
                        '_fields': 'id,title,content,date,modified,slug',
                        'context': 'edit'  # This gives us raw content instead of rendered
                    }
                    
                    response = await client.get(f"{self.base_url}/posts", params=params)
                    response.raise_for_status()
                    
                    posts = response.json()
                    if not posts:
                        break
                    
                    articles.extend(posts)
                    self.logger.info(f"Fetched {len(posts)} articles from page {page}")
                    
                    # Check if we have more pages
                    total_pages = int(response.headers.get('X-WP-TotalPages', 1))
                    if page >= total_pages:
                        break
                    
                    page += 1
                    
        except Exception as e:
            self.logger.error(f"Error fetching articles from {self.domain}: {e}")
            return []
        
        self.logger.info(f"Total articles fetched from {self.domain}: {len(articles)}")
        return articles
    
    async def backup_article(self, article: Dict[str, Any]) -> bool:
        """
        Create a backup of the article content
        
        Args:
            article: Article dictionary from WordPress API
            
        Returns:
            True if backup was successful
        """
        try:
            backup_dir = AFFILIATE_BACKUP_DIR / self.domain
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = backup_dir / f"article_{article['id']}_{timestamp}.json"
            
            backup_data = {
                'id': article['id'],
                'title': article['title'],
                'content': article['content'],
                'date': article['date'],
                'modified': article['modified'],
                'slug': article['slug'],
                'backup_timestamp': timestamp,
                'domain': self.domain
            }
            
            async with aiofiles.open(backup_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(backup_data, indent=2, ensure_ascii=False))
            
            self.logger.info(f"Backup created: {backup_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating backup for article {article['id']}: {e}")
            return False
    
    async def update_article_content(self, article_id: int, new_content: str) -> bool:
        """
        Update article content in WordPress
        
        Args:
            article_id: WordPress post ID
            new_content: Updated content with AAWP shortcodes
            
        Returns:
            True if update was successful
        """
        try:
            async with httpx.AsyncClient(auth=self.auth, timeout=30.0) as client:
                payload = {'content': new_content}
                
                response = await client.post(
                    f"{self.base_url}/posts/{article_id}",
                    json=payload
                )
                response.raise_for_status()
                
                self.logger.info(f"Successfully updated article {article_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error updating article {article_id}: {e}")
            return False

class ContentProcessor:
    """Processes article content to add AAWP shortcodes"""

    def __init__(self, openai_client: OpenAIClient):
        self.openai_client = openai_client
        self.logger = logging.getLogger(__name__)

    def insert_aawp_shortcodes_pattern_based(self, content: str, aawp_data: Dict[str, List[str]]) -> str:
        """
        Insert AAWP shortcodes after first paragraph of each H2 section using pattern-based approach.

        This works with the predictable Gutenberg block structure from 4_article_conversion_test.py:
        - H2 heading block
        - Image block
        - First paragraph block (INSERT AAWP AFTER THIS)
        - Additional paragraph blocks

        Args:
            content: Gutenberg block formatted content from WordPress
            aawp_data: Dictionary mapping H2 heading text to list of product names

        Returns:
            Content with AAWP shortcode blocks inserted
        """
        if not aawp_data:
            self.logger.info("No AAWP data provided, returning original content")
            return content

        # Pattern to match H2 section: Heading → Image → First Paragraph
        h2_section_pattern = r'(<!-- wp:heading -->\s*<h2 class="wp-block-heading">(.*?)</h2>\s*<!-- /wp:heading -->\s*<!-- wp:image -->.*?<!-- /wp:image -->\s*)(<!-- wp:paragraph -->\s*<p>.*?</p>\s*<!-- /wp:paragraph -->)'

        def replace_h2_section(match):
            heading_block = match.group(1)  # H2 + Image blocks
            heading_text = match.group(2).strip()  # Just the H2 text
            first_paragraph = match.group(3)  # First paragraph block

            # Find matching AAWP data for this heading
            matching_products = None

            # Try exact match first
            for aawp_heading, products in aawp_data.items():
                if heading_text.strip() == aawp_heading.strip():
                    matching_products = products
                    self.logger.info(f"Exact match found for '{heading_text}': {products}")
                    break

            # If no exact match, try fuzzy matching
            if not matching_products:
                clean_heading = re.sub(r'[^\w\s]', '', heading_text).strip().lower()
                clean_heading = re.sub(r'^\d+\.\s*', '', clean_heading)  # Remove leading numbers

                for aawp_heading, products in aawp_data.items():
                    clean_aawp_heading = re.sub(r'[^\w\s]', '', aawp_heading).strip().lower()

                    # Check for word overlap (at least 40% match)
                    heading_words = set(clean_heading.split())
                    aawp_words = set(clean_aawp_heading.split())

                    if heading_words and aawp_words:
                        match_ratio = len(heading_words.intersection(aawp_words)) / len(heading_words.union(aawp_words))
                        if match_ratio >= 0.4:
                            matching_products = products
                            self.logger.info(f"Fuzzy match found for '{heading_text}' -> '{aawp_heading}': {products}")
                            break

            # If we found matching products, insert AAWP shortcodes
            if matching_products:
                aawp_blocks = []
                for product in matching_products:
                    aawp_block = f'''<!-- wp:shortcode -->
[amazon bestseller="{product}" filterby="price" filter="1000" filter_compare="less" items="10" template="table" tracking_id="{AAWP_TRACKING_ID}"]
<!-- /wp:shortcode -->'''
                    aawp_blocks.append(aawp_block)

                # Return: Heading + Image + First Paragraph + AAWP Shortcodes
                result = heading_block + first_paragraph + '\n\n' + '\n\n'.join(aawp_blocks)
                self.logger.info(f"Inserted {len(matching_products)} AAWP shortcodes after first paragraph of '{heading_text}'")
                return result
            else:
                # No match found, return original
                self.logger.debug(f"No AAWP match found for heading: '{heading_text}'")
                return heading_block + first_paragraph

        # Apply the replacement to all H2 sections
        modified_content = re.sub(h2_section_pattern, replace_h2_section, content, flags=re.DOTALL)

        self.logger.info("Successfully processed content with pattern-based AAWP insertion")
        return modified_content

    def html_to_markdown(self, html_content: str) -> str:
        """
        Convert HTML elements to markdown format for existing conversion pipeline

        This function implements Phase 1 of the HTML to Gutenberg conversion approach,
        converting HTML content to markdown format that can then be processed by the
        existing proven process_gutenberg_content() function.

        Args:
            html_content: HTML content from WordPress API

        Returns:
            Markdown formatted content
        """
        content = html_content.strip()

        # Handle WordPress block comments - remove them as they'll be regenerated
        content = re.sub(r'<!-- wp:[^>]+ -->', '', content)
        content = re.sub(r'<!-- /wp:[^>]+ -->', '', content)

        # Convert headings: <h2 class="wp-block-heading">Text</h2> → ## Text
        content = re.sub(r'<h1[^>]*>(.*?)</h1>', r'# \1', content, flags=re.DOTALL)
        content = re.sub(r'<h2[^>]*>(.*?)</h2>', r'## \1', content, flags=re.DOTALL)
        content = re.sub(r'<h3[^>]*>(.*?)</h3>', r'### \1', content, flags=re.DOTALL)
        content = re.sub(r'<h4[^>]*>(.*?)</h4>', r'#### \1', content, flags=re.DOTALL)
        content = re.sub(r'<h5[^>]*>(.*?)</h5>', r'##### \1', content, flags=re.DOTALL)
        content = re.sub(r'<h6[^>]*>(.*?)</h6>', r'###### \1', content, flags=re.DOTALL)

        # Convert bold text: <strong>Text</strong> → **Text**
        content = re.sub(r'<strong[^>]*>(.*?)</strong>', r'**\1**', content, flags=re.DOTALL)
        content = re.sub(r'<b[^>]*>(.*?)</b>', r'**\1**', content, flags=re.DOTALL)

        # Convert italic text: <em>Text</em> → *Text*
        content = re.sub(r'<em[^>]*>(.*?)</em>', r'*\1*', content, flags=re.DOTALL)
        content = re.sub(r'<i[^>]*>(.*?)</i>', r'*\1*', content, flags=re.DOTALL)

        # Convert lists: <ul><li>Item</li></ul> → - Item
        # First handle nested list items
        content = re.sub(r'<li[^>]*>(.*?)</li>', r'- \1', content, flags=re.DOTALL)
        # Remove ul/ol tags
        content = re.sub(r'</?ul[^>]*>', '', content)
        content = re.sub(r'</?ol[^>]*>', '', content)

        # Convert paragraphs: <p>Content</p> → Content (with proper spacing)
        # Handle figure blocks (images) - preserve them for now, they'll be handled later
        content = re.sub(r'<figure[^>]*class="wp-block-image"[^>]*>(.*?)</figure>', r'\1', content, flags=re.DOTALL)

        # Convert paragraphs - remove p tags but preserve content
        content = re.sub(r'<p[^>]*>(.*?)</p>', r'\1', content, flags=re.DOTALL)

        # Clean up remaining HTML tags (except img tags which we might want to preserve)
        content = re.sub(r'<(?!img)[^>]+>', '', content)

        # Clean up HTML entities
        content = content.replace('&#8217;', "'")
        content = content.replace('&#8216;', "'")
        content = content.replace('&#8220;', '"')
        content = content.replace('&#8221;', '"')
        content = content.replace('&#8211;', '-')
        content = content.replace('&#8212;', '--')
        content = content.replace('&amp;', '&')
        content = content.replace('&lt;', '<')
        content = content.replace('&gt;', '>')
        content = content.replace('&nbsp;', ' ')

        # Clean up whitespace and line breaks
        # Replace multiple spaces with single space
        content = re.sub(r' +', ' ', content)
        # Replace multiple newlines with double newlines (markdown paragraph separation)
        content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
        # Clean up leading/trailing whitespace on lines
        lines = [line.strip() for line in content.split('\n')]
        content = '\n'.join(lines)

        # Ensure proper markdown spacing between blocks
        # Add double newlines before headings if not already present
        content = re.sub(r'(?<!\n\n)(^|\n)(#{1,6} )', r'\n\n\2', content, flags=re.MULTILINE)
        # Add double newlines before list items if not already present
        content = re.sub(r'(?<!\n\n)(^|\n)(- )', r'\n\n\2', content, flags=re.MULTILINE)

        # Final cleanup
        content = content.strip()

        self.logger.info("Successfully converted HTML to Markdown format")
        return content

    def parse_gutenberg_blocks(self, content: str) -> List[str]:
        """
        Parse content into blocks (handles both Gutenberg and HTML)

        Args:
            content: Raw content (Gutenberg blocks or HTML)

        Returns:
            List of content blocks
        """
        # First try to split by Gutenberg block patterns
        if '<!-- wp:' in content:
            # Split by double newlines for Gutenberg blocks
            blocks = re.split(r'\n\s*\n', content)
        else:
            # For HTML content, split by paragraph and heading tags
            # Split on closing tags followed by opening tags
            blocks = re.split(r'</(?:p|h[1-6]|figure)>\s*(?=<(?:p|h[1-6]|figure))', content)
            # Also split on standalone tags - use simpler approach
            temp_blocks = []
            for block in blocks:
                # Split on closing tag followed by opening tag pattern
                sub_blocks = re.split(r'(</(?:p|h[1-6]|figure)>)\s*(<(?:p|h[1-6]|figure)[^>]*>)', block)
                # Rejoin the split parts properly
                current_block = ""
                for part in sub_blocks:
                    if part.startswith('</'):
                        current_block += part
                        if current_block.strip():
                            temp_blocks.append(current_block.strip())
                        current_block = ""
                    elif part.startswith('<'):
                        current_block = part
                    else:
                        current_block += part
                if current_block.strip():
                    temp_blocks.append(current_block.strip())
            blocks = temp_blocks if temp_blocks else blocks

        return [block.strip() for block in blocks if block.strip()]

    def is_h2_heading_block(self, block: str) -> bool:
        """Check if block is an H2 heading"""
        # Check for Gutenberg block format
        if block.startswith('<!-- wp:heading -->') and '<h2 class="wp-block-heading">' in block:
            return True
        # Check for HTML format
        if '<h2' in block and 'wp-block-heading' in block:
            return True
        return False

    def extract_heading_text(self, block: str) -> str:
        """Extract text from H2 heading block"""
        match = re.search(r'<h2[^>]*>(.*?)</h2>', block)
        return match.group(1) if match else ""

    def is_paragraph_block(self, block: str) -> bool:
        """Check if block is a paragraph"""
        # Check for Gutenberg block format
        if block.startswith('<!-- wp:paragraph -->'):
            return True
        # Check for HTML format
        if block.startswith('<p>') or ('<p>' in block and not block.startswith('<h')):
            return True
        # Check for plain text (no HTML tags)
        if not block.startswith('<!-- wp:') and not block.startswith('<'):
            return True
        return False

    def create_aawp_shortcode(self, product_name: str) -> str:
        """
        Create AAWP shortcode for a product

        Args:
            product_name: Name of the product

        Returns:
            AAWP shortcode as Gutenberg block
        """
        return f'''<!-- wp:shortcode -->
[amazon bestseller="{product_name}" filterby="price" filter="1000" filter_compare="less" items="10" template="table" tracking_id="{AAWP_TRACKING_ID}"]
<!-- /wp:shortcode -->'''

    async def process_h2_section(self, heading_block: str, paragraphs: List[str]) -> Tuple[str, List[str]]:
        """
        Process an H2 section to add AAWP shortcodes

        Args:
            heading_block: H2 heading block
            paragraphs: List of paragraph blocks following the heading

        Returns:
            Tuple of (heading_block, modified_paragraphs)
        """
        if len(paragraphs) < 2:
            self.logger.warning("H2 section has fewer than 2 paragraphs, skipping")
            return heading_block, paragraphs

        # Extract heading text and second paragraph content
        heading_text = self.extract_heading_text(heading_block)

        # Extract text from second paragraph (remove markup)
        second_paragraph = paragraphs[1]
        # Remove Gutenberg block markup
        paragraph_text = re.sub(r'<!-- wp:paragraph -->\s*', '', second_paragraph)
        paragraph_text = re.sub(r'\s*<!-- /wp:paragraph -->', '', paragraph_text)
        # Remove HTML tags
        paragraph_text = re.sub(r'<[^>]+>', '', paragraph_text)
        # Clean up HTML entities
        paragraph_text = paragraph_text.replace('&#8217;', "'").replace('&amp;', '&')
        paragraph_text = paragraph_text.strip()

        # Get product suggestions from OpenAI
        try:
            products = await self.openai_client.analyze_products(heading_text, paragraph_text)
            self.logger.info(f"OpenAI suggested products for '{heading_text}': {products}")
        except Exception as e:
            self.logger.error(f"Error getting product suggestions: {e}")
            products = []  # Return empty list instead of fallback products

        # Create AAWP shortcodes
        shortcodes = [self.create_aawp_shortcode(product) for product in products]

        # Insert shortcodes after first paragraph
        modified_paragraphs = [paragraphs[0]]  # First paragraph
        modified_paragraphs.extend(shortcodes)  # Add shortcodes
        modified_paragraphs.extend(paragraphs[1:])  # Rest of paragraphs

        return heading_block, modified_paragraphs

    def parse_h2_sections_from_content(self, content: str) -> List[Dict[str, str]]:
        """
        Parse H2 sections from Gutenberg content to extract headings and second paragraphs.

        Args:
            content: Gutenberg block formatted content

        Returns:
            List of sections with heading and second paragraph text
        """
        sections = []

        # Pattern to find H2 sections with heading and paragraphs
        h2_section_pattern = r'<!-- wp:heading -->\s*<h2 class="wp-block-heading">(.*?)</h2>\s*<!-- /wp:heading -->(.*?)(?=<!-- wp:heading --|$)'

        h2_matches = re.findall(h2_section_pattern, content, re.DOTALL)

        for heading_text, section_content in h2_matches:
            heading_text = heading_text.strip()

            # Extract paragraphs from this section
            paragraph_pattern = r'<!-- wp:paragraph -->\s*<p>(.*?)</p>\s*<!-- /wp:paragraph -->'
            paragraphs = re.findall(paragraph_pattern, section_content, re.DOTALL)

            # Clean paragraphs and get second one if available
            clean_paragraphs = []
            for p in paragraphs:
                # Remove HTML tags and clean up
                clean_p = re.sub(r'<[^>]+>', '', p).strip()
                clean_p = clean_p.replace('&#8217;', "'").replace('&amp;', '&').replace('â€™', "'").replace('â€"', '-')
                if clean_p:
                    clean_paragraphs.append(clean_p)

            if len(clean_paragraphs) >= 2:
                sections.append({
                    'heading': heading_text,
                    'second_paragraph': clean_paragraphs[1]
                })
                self.logger.debug(f"Found H2 section: '{heading_text}' with second paragraph")

        return sections

    async def process_article_content(self, content: str) -> str:
        """
        Process article content using simple pattern-based AAWP insertion.

        This works with the predictable Gutenberg block structure from 4_article_conversion_test.py.
        Much simpler than the previous complex parsing approach.

        Args:
            content: Gutenberg block formatted content from WordPress

        Returns:
            Content with AAWP shortcodes inserted after first paragraph of each H2 section
        """
        try:
            # Step 1: Parse H2 sections to get headings and second paragraphs for OpenAI
            sections = self.parse_h2_sections_from_content(content)

            if not sections:
                self.logger.warning("No H2 sections with sufficient paragraphs found")
                return content

            # Step 2: Generate AAWP data using OpenAI for each section
            aawp_data = {}
            for section in sections:
                try:
                    products = await self.openai_client.analyze_products(
                        section['heading'],
                        section['second_paragraph']
                    )
                    aawp_data[section['heading']] = products
                    self.logger.info(f"Generated AAWP products for '{section['heading']}': {products}")
                except Exception as e:
                    self.logger.error(f"Error generating AAWP products for '{section['heading']}': {e}")
                    aawp_data[section['heading']] = []  # Return empty list instead of fallback products

            # Step 3: Insert AAWP shortcodes using pattern-based approach
            modified_content = self.insert_aawp_shortcodes_pattern_based(content, aawp_data)

            self.logger.info(f"Successfully processed article with {len(sections)} H2 sections and {sum(len(products) for products in aawp_data.values())} AAWP products")
            return modified_content

        except Exception as e:
            self.logger.error(f"Error in process_article_content: {e}")
            # Return original content if processing fails
            return content

class AmazonAffiliateIntegrator:
    """Main class for Amazon affiliate integration"""

    def __init__(self):
        self.logger = setup_logging()
        self.openai_client = OpenAIClient(OPENAI_API_KEY)
        self.content_processor = ContentProcessor(self.openai_client)

    async def process_domain(self, domain: str, dry_run: bool = False, limit: Optional[int] = None) -> Dict[str, Any]:
        """
        Process all articles for a specific domain

        Args:
            domain: Domain to process
            dry_run: If True, don't actually update articles
            limit: Maximum number of articles to process (for testing)

        Returns:
            Processing results
        """
        if domain not in WP_CREDENTIALS or not WP_CREDENTIALS[domain]:
            self.logger.error(f"No credentials found for domain: {domain}")
            return {'success': False, 'error': 'No credentials'}

        wp_client = WordPressClient(domain, WP_CREDENTIALS[domain])

        # Fetch articles
        articles = await wp_client.fetch_articles_since_january_2025()
        if not articles:
            self.logger.info(f"No articles found for {domain}")
            return {'success': True, 'processed': 0, 'articles': []}

        results = {
            'success': True,
            'processed': 0,
            'failed': 0,
            'articles': []
        }

        # Apply limit if specified
        if limit:
            articles = articles[:limit]
            self.logger.info(f"Limited to {len(articles)} articles for testing")

        for article in articles:
            try:
                self.logger.info(f"Processing article {article['id']}: {article['title']['rendered']}")

                # Create backup
                if not await wp_client.backup_article(article):
                    self.logger.error(f"Failed to backup article {article['id']}, skipping")
                    results['failed'] += 1
                    continue

                # Process content - use raw content if available, otherwise rendered
                if 'raw' in article['content']:
                    original_content = article['content']['raw']
                else:
                    original_content = article['content']['rendered']
                modified_content = await self.content_processor.process_article_content(original_content)

                if dry_run:
                    self.logger.info(f"DRY RUN: Would update article {article['id']}")
                    results['processed'] += 1
                else:
                    # Update article
                    if await wp_client.update_article_content(article['id'], modified_content):
                        results['processed'] += 1
                        self.logger.info(f"Successfully processed article {article['id']}")
                    else:
                        results['failed'] += 1
                        self.logger.error(f"Failed to update article {article['id']}")

                results['articles'].append({
                    'id': article['id'],
                    'title': article['title']['rendered'],
                    'status': 'processed' if not dry_run else 'dry_run'
                })

            except Exception as e:
                self.logger.error(f"Error processing article {article['id']}: {e}")
                results['failed'] += 1

        return results

    async def restore_article(self, domain: str, post_id: int) -> bool:
        """
        Restore an article from backup

        Args:
            domain: Domain of the article
            post_id: WordPress post ID

        Returns:
            True if restore was successful
        """
        try:
            backup_dir = AFFILIATE_BACKUP_DIR / domain
            if not backup_dir.exists():
                self.logger.error(f"No backup directory found for {domain}")
                return False

            # Find the most recent backup for this post
            backup_files = list(backup_dir.glob(f"article_{post_id}_*.json"))
            if not backup_files:
                self.logger.error(f"No backup found for article {post_id}")
                return False

            # Get the most recent backup
            latest_backup = max(backup_files, key=lambda x: x.stat().st_mtime)

            # Load backup data
            async with aiofiles.open(latest_backup, 'r', encoding='utf-8') as f:
                backup_data = json.loads(await f.read())

            # Restore the article
            wp_client = WordPressClient(domain, WP_CREDENTIALS[domain])
            success = await wp_client.update_article_content(post_id, backup_data['content']['rendered'])

            if success:
                self.logger.info(f"Successfully restored article {post_id} from {latest_backup}")
            else:
                self.logger.error(f"Failed to restore article {post_id}")

            return success

        except Exception as e:
            self.logger.error(f"Error restoring article {post_id}: {e}")
            return False

    async def process_all_domains(self, dry_run: bool = False) -> Dict[str, Any]:
        """
        Process all configured domains

        Args:
            dry_run: If True, don't actually update articles

        Returns:
            Overall processing results
        """
        overall_results = {
            'success': True,
            'domains': {},
            'total_processed': 0,
            'total_failed': 0
        }

        for domain in WP_CREDENTIALS:
            if WP_CREDENTIALS[domain]:
                self.logger.info(f"Processing domain: {domain}")
                results = await self.process_domain(domain, dry_run)
                overall_results['domains'][domain] = results
                overall_results['total_processed'] += results.get('processed', 0)
                overall_results['total_failed'] += results.get('failed', 0)
            else:
                self.logger.warning(f"Skipping {domain} - no credentials")

        return overall_results

def test_html_to_markdown():
    """Test function to validate HTML to Markdown conversion"""
    print("Testing HTML to Markdown conversion...")

    # Create a test processor
    openai_client = OpenAIClient("test-key")  # Dummy key for testing
    processor = ContentProcessor(openai_client)

    # Test cases
    test_cases = [
        {
            'name': 'Basic heading conversion',
            'html': '<h2 class="wp-block-heading">Living Room Ideas</h2>',
            'expected_contains': '## Living Room Ideas'
        },
        {
            'name': 'Paragraph conversion',
            'html': '<p>This is a test paragraph with some content.</p>',
            'expected_contains': 'This is a test paragraph with some content.'
        },
        {
            'name': 'Bold text conversion',
            'html': '<p>This has <strong>bold text</strong> in it.</p>',
            'expected_contains': '**bold text**'
        },
        {
            'name': 'List conversion',
            'html': '<ul><li>First item</li><li>Second item</li></ul>',
            'expected_contains': '- First item'
        },
        {
            'name': 'Complex HTML with Gutenberg blocks',
            'html': '''<!-- wp:heading -->
<h2 class="wp-block-heading">Modern Furniture Ideas</h2>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>Transform your space with <strong>contemporary designs</strong> that blend style and functionality.</p>
<!-- /wp:paragraph -->

<!-- wp:list -->
<ul class="wp-block-list">
<li>Minimalist sofas</li>
<li>Glass coffee tables</li>
</ul>
<!-- /wp:list -->''',
            'expected_contains': ['## Modern Furniture Ideas', '**contemporary designs**', '- Minimalist sofas']
        }
    ]

    all_passed = True
    for test_case in test_cases:
        try:
            result = processor.html_to_markdown(test_case['html'])

            if isinstance(test_case['expected_contains'], list):
                # Multiple expected strings
                passed = all(expected in result for expected in test_case['expected_contains'])
                if passed:
                    print(f"✓ {test_case['name']}: PASSED")
                else:
                    print(f"✗ {test_case['name']}: FAILED")
                    print(f"  Result: {repr(result)}")
                    print(f"  Expected to contain: {test_case['expected_contains']}")
                    all_passed = False
            else:
                # Single expected string
                if test_case['expected_contains'] in result:
                    print(f"✓ {test_case['name']}: PASSED")
                else:
                    print(f"✗ {test_case['name']}: FAILED")
                    print(f"  Result: {repr(result)}")
                    print(f"  Expected to contain: {test_case['expected_contains']}")
                    all_passed = False

        except Exception as e:
            print(f"✗ {test_case['name']}: ERROR - {e}")
            all_passed = False

    if all_passed:
        print("\n🎉 All HTML to Markdown conversion tests PASSED!")
    else:
        print("\n❌ Some tests FAILED. Please review the implementation.")

    return all_passed

def test_pattern_based_aawp_insertion():
    """Test the new pattern-based AAWP insertion with actual Gutenberg content"""
    print("Testing Pattern-Based AAWP Insertion...")

    # Create a test processor
    openai_client = OpenAIClient("test-key")  # Dummy key for testing
    processor = ContentProcessor(openai_client)

    # Real Gutenberg content structure (based on actual converted articles)
    gutenberg_content = '''<!-- wp:paragraph -->
<p>Looking to maximize your bedroom space without sacrificing style? Space-saving furniture ideas have become incredibly popular.</p>
<!-- /wp:paragraph -->

<!-- wp:heading -->
<h2 class="wp-block-heading">1. Fold-Down Murphy Bed with Built-In Storage</h2>
<!-- /wp:heading -->

<!-- wp:image -->
<figure class="wp-block-image"><img src="https://example.com/image1.jpg" /></figure>
<!-- /wp:image -->

<!-- wp:paragraph -->
<p>Ever wish your tiny bedroom could feel bigger without sacrificing comfort? A fold-down Murphy bed might just be your new best friend.</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph -->
<p>Picture a sleek wall that seamlessly blends into your decor, with a mattress folded flush against it. Consider installing premium storage solutions and luxury bedding accessories.</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph -->
<p>You can customize the finish—light oak for a Scandinavian vibe or dark walnut for a more luxe look.</p>
<!-- /wp:paragraph -->

<!-- wp:heading -->
<h2 class="wp-block-heading">2. Multi-Functional Loft Bed with Study Desk</h2>
<!-- /wp:heading -->

<!-- wp:image -->
<figure class="wp-block-image"><img src="https://example.com/image2.jpg" /></figure>
<!-- /wp:image -->

<!-- wp:paragraph -->
<p>Struggling with a small bedroom that doubles as a study area or workspace? A multi-functional loft bed is the ultimate space hack.</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph -->
<p>It elevates your sleeping zone, freeing up room underneath for a desk or lounge area. Modern loft beds and ergonomic desk chairs work perfectly together.</p>
<!-- /wp:paragraph -->'''

    # AAWP data for testing
    aawp_data = {
        'Fold-Down Murphy Bed with Built-In Storage': ['Murphy Bed Frame', 'Storage Ottoman'],
        'Multi-Functional Loft Bed with Study Desk': ['Loft Bed', 'Desk Chair']
    }

    test_cases = [
        {
            'name': 'Pattern-based AAWP insertion with real Gutenberg content',
            'content': gutenberg_content,
            'aawp_data': aawp_data,
            'expected_contains': [
                '<!-- wp:shortcode -->',
                '[amazon bestseller="Murphy Bed Frame"',
                '[amazon bestseller="Storage Ottoman"',
                '[amazon bestseller="Loft Bed"',
                '[amazon bestseller="Desk Chair"',
                'tracking_id="piatto-20"'
            ]
        },
        {
            'name': 'H2 section parsing from Gutenberg content',
            'content': gutenberg_content,
            'test_type': 'section_parsing',
            'expected_sections': 2,
            'expected_headings': ['1. Fold-Down Murphy Bed with Built-In Storage', '2. Multi-Functional Loft Bed with Study Desk']
        }
    ]

    all_passed = True
    for test_case in test_cases:
        try:
            if test_case.get('test_type') == 'section_parsing':
                # Test section parsing
                sections = processor.parse_h2_sections_from_content(test_case['content'])

                if len(sections) == test_case['expected_sections']:
                    print(f"✓ {test_case['name']}: Found {len(sections)} sections")

                    # Check if expected headings are found
                    found_headings = [s['heading'] for s in sections]
                    headings_match = all(heading in found_headings for heading in test_case['expected_headings'])

                    if headings_match:
                        print(f"✓ All expected headings found: {found_headings}")
                    else:
                        print(f"✗ Heading mismatch. Expected: {test_case['expected_headings']}, Found: {found_headings}")
                        all_passed = False
                else:
                    print(f"✗ {test_case['name']}: Expected {test_case['expected_sections']} sections, found {len(sections)}")
                    all_passed = False

            else:
                # Test AAWP insertion
                result = processor.insert_aawp_shortcodes_pattern_based(test_case['content'], test_case['aawp_data'])

                # Check if all expected strings are present
                passed = all(expected in result for expected in test_case['expected_contains'])

                if passed:
                    print(f"✓ {test_case['name']}: PASSED")
                    # Count shortcodes inserted
                    shortcode_count = result.count('<!-- wp:shortcode -->')
                    print(f"  → Inserted {shortcode_count} AAWP shortcode blocks")
                else:
                    print(f"✗ {test_case['name']}: FAILED")
                    print(f"  Expected to contain: {test_case['expected_contains']}")
                    # Show a snippet of the result
                    print(f"  Result snippet: {result[:300]}...")
                    all_passed = False

        except Exception as e:
            print(f"✗ {test_case['name']}: ERROR - {e}")
            all_passed = False

    if all_passed:
        print("\n🎉 All pattern-based AAWP insertion tests PASSED!")
    else:
        print("\n❌ Some tests FAILED. Please review the implementation.")

    return all_passed

async def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(description='Amazon Affiliate Integration for WordPress')
    parser.add_argument('--domain', help='Process specific domain only')
    parser.add_argument('--dry-run', action='store_true', help='Dry run - don\'t actually update articles')
    parser.add_argument('--restore', type=int, help='Restore article by post ID (requires --domain)')
    parser.add_argument('--list-backups', action='store_true', help='List available backups')
    parser.add_argument('--limit', type=int, help='Limit number of articles to process (for testing)')
    parser.add_argument('--test-conversion', action='store_true', help='Test HTML to Markdown conversion functionality')
    parser.add_argument('--test-pattern', action='store_true', help='Test pattern-based AAWP insertion with real Gutenberg content')

    args = parser.parse_args()

    integrator = AmazonAffiliateIntegrator()

    try:
        if args.test_conversion:
            # Run HTML to Markdown conversion tests
            success = test_html_to_markdown()
            return 0 if success else 1

        if args.test_pattern:
            # Run pattern-based AAWP insertion tests
            success = test_pattern_based_aawp_insertion()
            return 0 if success else 1

        if args.list_backups:
            # List all backups
            if AFFILIATE_BACKUP_DIR.exists():
                for domain_dir in AFFILIATE_BACKUP_DIR.iterdir():
                    if domain_dir.is_dir():
                        print(f"\n{domain_dir.name}:")
                        for backup_file in domain_dir.glob("*.json"):
                            print(f"  {backup_file.name}")
            else:
                print("No backups found")
            return 0

        if args.restore:
            if not args.domain:
                print("Error: --domain is required when using --restore")
                return 1

            success = await integrator.restore_article(args.domain, args.restore)
            return 0 if success else 1

        if args.domain:
            # Process specific domain
            results = await integrator.process_domain(args.domain, args.dry_run, args.limit)
            print(f"\nResults for {args.domain}:")
            print(f"  Processed: {results.get('processed', 0)}")
            print(f"  Failed: {results.get('failed', 0)}")
        else:
            # Process all domains
            results = await integrator.process_all_domains(args.dry_run)
            print(f"\nOverall Results:")
            print(f"  Total Processed: {results['total_processed']}")
            print(f"  Total Failed: {results['total_failed']}")

            for domain, domain_results in results['domains'].items():
                print(f"  {domain}: {domain_results.get('processed', 0)} processed, {domain_results.get('failed', 0)} failed")

        return 0

    except Exception as e:
        logging.error(f"Fatal error: {e}")
        return 1

if __name__ == "__main__":
    if not OPENAI_API_KEY:
        print("Error: OPENAI_API_KEY not found in environment variables")
        sys.exit(1)

    sys.exit(asyncio.run(main()))
