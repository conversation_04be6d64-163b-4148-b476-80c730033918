"""
Data models for Amazon Affiliate Integration

Defines data structures for processing state, article results, crawler results,
and other system components.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Set, Any
from enum import Enum

# =============================================================================
# ENUMS
# =============================================================================

class ProcessingStatus(Enum):
    """Status of article processing"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class CrawlerStatus(Enum):
    """Status of crawler operations"""
    NOT_CRAWLED = "not_crawled"
    CRAWLING = "crawling"
    CRAWLED = "crawled"
    FAILED = "failed"

class ShortcodeStatus(Enum):
    """Status of shortcode detection"""
    WORKING = "working"      # Shortcode is working (not visible)
    VISIBLE = "visible"      # Shortcode is visible (failed)
    UNKNOWN = "unknown"      # Status not determined

# =============================================================================
# PROCESSING STATE MODELS
# =============================================================================

@dataclass
class ProcessingState:
    """
    Overall processing state for the system
    """
    processed_urls: Set[str] = field(default_factory=set)
    excluded_urls: Set[str] = field(default_factory=set)
    missing_products: Dict[str, Dict] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=datetime.now)
    total_processed: int = 0
    
    def is_processed(self, url: str) -> bool:
        """Check if URL has been processed"""
        return url in self.processed_urls
    
    def is_excluded(self, url: str) -> bool:
        """Check if URL is excluded from processing"""
        return url in self.excluded_urls
    
    def mark_processed(self, url: str) -> None:
        """Mark URL as processed"""
        self.processed_urls.add(url)
        self.total_processed += 1
        self.last_updated = datetime.now()
    
    def add_excluded(self, url: str) -> None:
        """Add URL to exclusion list"""
        self.excluded_urls.add(url)
        self.last_updated = datetime.now()

# =============================================================================
# ARTICLE PROCESSING MODELS
# =============================================================================

@dataclass
class ArticleInfo:
    """
    Information about a WordPress article
    """
    id: int
    title: str
    url: str
    content: str
    date: str
    modified: str
    slug: str
    domain: str

@dataclass
class ProductInfo:
    """
    Information about a product for shortcode generation
    """
    name: str
    heading: str
    section_index: int
    
@dataclass
class ShortcodeInfo:
    """
    Information about an inserted shortcode
    """
    product_name: str
    heading: str
    position: int
    template_used: str
    
@dataclass
class ArticleProcessingResult:
    """
    Result of processing a single article
    """
    article_id: int
    url: str
    domain: str
    status: ProcessingStatus
    shortcodes_added: List[ShortcodeInfo] = field(default_factory=list)
    products_generated: List[ProductInfo] = field(default_factory=list)
    error_message: Optional[str] = None
    processing_time: float = 0.0
    backup_created: bool = False
    modified_content: Optional[str] = None  # Store the modified content with shortcodes
    
    @property
    def success(self) -> bool:
        """Check if processing was successful"""
        return self.status == ProcessingStatus.COMPLETED
    
    @property
    def shortcode_count(self) -> int:
        """Get number of shortcodes added"""
        return len(self.shortcodes_added)

# =============================================================================
# CRAWLER MODELS
# =============================================================================

@dataclass
class VisibleShortcode:
    """
    Information about a visible (failed) shortcode
    """
    product_name: str
    heading: str
    full_shortcode: str
    position_in_content: int

@dataclass
class CrawlerResult:
    """
    Result of crawling a single article
    """
    url: str
    domain: str
    status: CrawlerStatus
    visible_shortcodes: List[VisibleShortcode] = field(default_factory=list)
    total_shortcodes_found: int = 0
    response_time: float = 0.0
    error_message: Optional[str] = None
    crawled_at: datetime = field(default_factory=datetime.now)
    
    @property
    def success(self) -> bool:
        """Check if crawling was successful"""
        return self.status == CrawlerStatus.CRAWLED
    
    @property
    def has_visible_shortcodes(self) -> bool:
        """Check if any shortcodes are visible (failed)"""
        return len(self.visible_shortcodes) > 0
    
    @property
    def failed_product_names(self) -> List[str]:
        """Get list of product names that failed"""
        return [sc.product_name for sc in self.visible_shortcodes]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'url': self.url,
            'domain': self.domain,
            'status': self.status.value,
            'visible_shortcodes': [
                {
                    'product_name': sc.product_name,
                    'heading': sc.heading,
                    'full_shortcode': sc.full_shortcode,
                    'position_in_content': sc.position_in_content
                } for sc in self.visible_shortcodes
            ],
            'total_shortcodes_found': self.total_shortcodes_found,
            'response_time': self.response_time,
            'error_message': self.error_message,
            'crawled_at': self.crawled_at.isoformat() if self.crawled_at else None,
            'success': self.success,
            'has_visible_shortcodes': self.has_visible_shortcodes,
            'failed_product_names': self.failed_product_names
        }

# =============================================================================
# CLEANUP MODELS
# =============================================================================

@dataclass
class CleanupAction:
    """
    Action to clean up a failed shortcode
    """
    url: str
    article_id: int
    shortcode_to_remove: str
    heading: str
    product_name: str

@dataclass
class CleanupResult:
    """
    Result of cleanup operations
    """
    url: str
    domain: str
    actions_performed: List[CleanupAction] = field(default_factory=list)
    success: bool = False
    error_message: Optional[str] = None
    cleanup_time: float = 0.0
    
    @property
    def shortcodes_removed(self) -> int:
        """Get number of shortcodes removed"""
        return len(self.actions_performed)

# =============================================================================
# BATCH PROCESSING MODELS
# =============================================================================

@dataclass
class BatchProcessingResult:
    """
    Result of batch processing operations
    """
    domain: str
    total_articles: int
    successful_results: List[ArticleProcessingResult] = field(default_factory=list)
    failed_results: List[ArticleProcessingResult] = field(default_factory=list)
    skipped_results: List[ArticleProcessingResult] = field(default_factory=list)
    total_processing_time: float = 0.0
    
    @property
    def success_count(self) -> int:
        """Get number of successful processing operations"""
        return len(self.successful_results)
    
    @property
    def failure_count(self) -> int:
        """Get number of failed processing operations"""
        return len(self.failed_results)
    
    @property
    def skip_count(self) -> int:
        """Get number of skipped processing operations"""
        return len(self.skipped_results)
    
    @property
    def success_rate(self) -> float:
        """Get success rate as percentage"""
        if self.total_articles == 0:
            return 0.0
        return (self.success_count / self.total_articles) * 100

@dataclass
class BatchCrawlerResult:
    """
    Result of batch crawling operations
    """
    domain: str
    total_urls: int
    successful_results: List[CrawlerResult] = field(default_factory=list)
    failed_results: List[CrawlerResult] = field(default_factory=list)
    total_crawling_time: float = 0.0
    
    @property
    def success_count(self) -> int:
        """Get number of successful crawling operations"""
        return len(self.successful_results)
    
    @property
    def failure_count(self) -> int:
        """Get number of failed crawling operations"""
        return len(self.failed_results)
    
    @property
    def urls_with_visible_shortcodes(self) -> List[str]:
        """Get URLs that have visible shortcodes"""
        return [result.url for result in self.successful_results if result.has_visible_shortcodes]
    
    @property
    def total_visible_shortcodes(self) -> int:
        """Get total number of visible shortcodes found"""
        return sum(len(result.visible_shortcodes) for result in self.successful_results)

# =============================================================================
# WORKFLOW MODELS
# =============================================================================

@dataclass
class ProcessingWorkflowResult:
    """
    Result of processing workflow execution (Process articles only)
    """
    domain: str
    total_articles: int
    articles_processed: int
    articles_failed: int
    articles_skipped: int
    processing_results: List[ArticleProcessingResult] = field(default_factory=list)
    update_results: List[Dict[str, Any]] = field(default_factory=list)
    processing_time: float = 0.0
    force_mode: bool = False
    dry_run: bool = False
    git_checkpoint_created: bool = False
    error_message: Optional[str] = None

    @property
    def success(self) -> bool:
        """Check if processing was successful"""
        return self.error_message is None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'success': self.success,
            'domain': self.domain,
            'total_articles': self.total_articles,
            'articles_processed': self.articles_processed,
            'articles_failed': self.articles_failed,
            'articles_skipped': self.articles_skipped,
            'processing_time': self.processing_time,
            'force_mode': self.force_mode,
            'dry_run': self.dry_run,
            'git_checkpoint_created': self.git_checkpoint_created,
            'error_message': self.error_message
        }

@dataclass
class WorkflowResult:
    """
    Result of complete workflow execution (Process → Crawl → Cleanup)
    """
    domain: str
    processing_result: BatchProcessingResult
    crawler_result: BatchCrawlerResult
    cleanup_result: Optional[List[CleanupResult]] = None
    total_workflow_time: float = 0.0
    workflow_success: bool = False
    
    def generate_summary(self) -> Dict[str, Any]:
        """Generate a summary of the workflow results"""
        return {
            'domain': self.domain,
            'articles_processed': self.processing_result.success_count,
            'articles_failed': self.processing_result.failure_count,
            'articles_skipped': self.processing_result.skip_count,
            'urls_crawled': self.crawler_result.success_count,
            'urls_with_issues': len(self.crawler_result.urls_with_visible_shortcodes),
            'shortcodes_cleaned': sum(r.shortcodes_removed for r in (self.cleanup_result or [])),
            'total_time': self.total_workflow_time,
            'success': self.workflow_success
        }
