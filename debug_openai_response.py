#!/usr/bin/env python3
"""
Debug script to test OpenAI API response for product generation
"""

import asyncio
import sys
from amazon_affiliate_integration.clients.openai_client import OpenAIClient

async def test_openai_response():
    """Test OpenAI API response with sample sections"""
    
    # Initialize OpenAI client
    client = OpenAIClient()
    
    # Test sections from the article
    test_sections = [
        {
            'heading': '1. Plush Upholstery and Overstuffed Sofas for Instant Comfort',
            'second_paragraph': 'When it comes to creating a warm and cozy living room, nothing beats the comfort of plush upholstery and overstuffed sofas. These pieces serve as the foundation of your seating area, providing both visual appeal and physical comfort. Look for sofas with deep cushions, soft fabrics like velvet or chenille, and generous proportions that invite you to sink in and relax.'
        },
        {
            'heading': '2. Layered Textiles: Using Throws, Cushions, and Rugs to Create Warmth',
            'second_paragraph': 'Layering textiles is one of the most effective ways to add warmth and coziness to your living room. Start with a foundation of soft area rugs, then add throw pillows in various textures and patterns. Drape cozy blankets over your seating, and consider adding curtains or drapes that complement your color scheme.'
        }
    ]
    
    print("🔍 Testing OpenAI API response...")
    print("=" * 60)
    
    try:
        # Test batch processing
        print("📋 Testing batch processing...")
        batch_results = await client.analyze_products_batch(test_sections)
        
        print(f"\n✅ Batch results received:")
        for heading, products in batch_results.items():
            print(f"  📝 {heading}")
            print(f"     Products: {products}")
        
        print("\n" + "=" * 60)
        
        # Test individual processing for comparison
        print("📋 Testing individual processing...")
        for section in test_sections:
            print(f"\n🔍 Processing: {section['heading']}")
            individual_results = await client.analyze_products(
                section['heading'], 
                section['second_paragraph']
            )
            print(f"     Products: {individual_results}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_openai_response())
