# Amazon Affiliate Integration Enhancement - System Architecture

## Architecture Overview

**Pattern**: Modular Microservice-Style Architecture with Orchestrated Workflows
**Approach**: Event-driven processing with state management and automated quality assurance

## System Components

### 1. Core Layer (`core/`)

#### Configuration Manager (`config.py`)
```python
# Centralized configuration management
CONCURRENCY_LIMIT = 4  # Configurable async processing limit
STATE_FILES = {
    'processed_urls': 'processed_urls.json',
    'excluded_urls': 'excluded_urls.json', 
    'missing_products': 'missing_products.json'
}
SHORTCODE_TEMPLATE = """<!-- wp:heading -->
<h3 class="wp-block-heading">Recommended Products to replicate this idea</h3>
<!-- /wp:heading -->
<!-- wp:shortcode -->
[amazon bestseller="{product}" filterby="price" filter="1000" filter_compare="less" items="1" template="table" tracking_id="{AAWP_TRACKING_ID}"]
<!-- /wp:shortcode -->"""
```

#### Data Models (`models.py`)
```python
@dataclass
class ProcessingState:
    processed_urls: Set[str]
    excluded_urls: Set[str]
    missing_products: Dict[str, List[Dict]]

@dataclass
class ArticleProcessingResult:
    article_id: int
    url: str
    success: bool
    shortcodes_added: int
    error: Optional[str]

@dataclass
class CrawlerResult:
    url: str
    visible_shortcodes: List[str]
    failed_products: List[str]
```

### 2. State Management Layer (`processors/state_manager.py`)

#### StateManager Class
- **Responsibility**: Manage processing state across runs
- **Key Methods**:
  - `load_state()`: Load JSON state files
  - `save_state()`: Atomic save with backup
  - `is_processed(url)`: Check if URL already processed
  - `is_excluded(url)`: Check if URL is excluded
  - `mark_processed(url)`: Add URL to processed list
  - `track_missing_product(url, heading, product)`: Track failed shortcodes

### 3. Processing Layer (`processors/content_processor.py`)

#### Enhanced ContentProcessor Class
- **New Methods**:
  - `create_product_section_block()`: Generate heading + shortcode blocks
  - `insert_shortcodes_with_heading()`: Insert heading once per H2 section
  - `process_articles_concurrently()`: Async processing with semaphore

#### Processing Flow
```
1. Load processing state
2. Filter out processed/excluded URLs (unless --force)
3. Process articles in batches of CONCURRENCY_LIMIT
4. Update state after each successful processing
5. Return processing results for crawler
```

### 4. Quality Assurance Layer (`crawlers/shortcode_crawler.py`)

#### ShortcodeCrawler Class
- **Responsibility**: Detect visible shortcodes on live articles
- **Key Methods**:
  - `crawl_article(url)`: HTTP request + HTML parsing
  - `detect_visible_shortcodes(html)`: Regex pattern matching
  - `crawl_articles_batch()`: Concurrent crawling
  - `generate_cleanup_plan()`: Identify shortcodes to remove

#### Crawler Implementation
```python
SHORTCODE_REGEX = r'\[amazon bestseller="([^"]+)"[^\]]*\]'

async def detect_visible_shortcodes(self, html: str) -> List[str]:
    """Find shortcodes that are visible as text (failed to render)"""
    matches = re.findall(self.SHORTCODE_REGEX, html)
    return [match for match in matches if self.is_shortcode_visible(html, match)]
```

### 5. Client Layer (`clients/`)

#### WordPressClient (Enhanced)
- **New Methods**:
  - `batch_update_articles()`: Concurrent article updates
  - `remove_shortcodes()`: Remove specific shortcodes from content
  - `restore_articles_batch()`: Concurrent restoration

#### OpenAIClient (Unchanged)
- Existing functionality maintained

### 6. Utilities Layer (`utils/`)

#### BackupManager (`backup_manager.py`)
- **Enhanced Features**:
  - Gutenberg block format validation
  - Batch backup operations
  - Restore with concurrency control

#### Helpers (`helpers.py`)
- URL validation and normalization
- Regex utilities for shortcode detection
- File I/O helpers with atomic operations

### 7. Orchestration Layer (`main.py`)

#### WorkflowOrchestrator Class
```python
class WorkflowOrchestrator:
    async def execute_full_workflow(self, args):
        """Main workflow: Process → Crawl → Cleanup"""
        
        # Phase 1: Process Articles
        processing_results = await self.process_articles(args)
        
        # Phase 2: Crawl for Failed Shortcodes
        crawler_results = await self.crawl_articles(processing_results)
        
        # Phase 3: Cleanup Failed Shortcodes
        cleanup_results = await self.cleanup_failed_shortcodes(crawler_results)
        
        return self.generate_final_report(processing_results, crawler_results, cleanup_results)
```

## Data Flow Architecture

### 1. Processing Workflow
```
Input: WordPress Articles
↓
StateManager: Check processed/excluded status
↓
ContentProcessor: Add shortcodes (4 concurrent)
↓
StateManager: Update processed URLs
↓
Output: Processing Results
```

### 2. Crawling Workflow
```
Input: Processed Article URLs
↓
ShortcodeCrawler: HTTP requests (4 concurrent)
↓
HTML Parser: Detect visible shortcodes
↓
StateManager: Track missing products
↓
Output: Crawler Results
```

### 3. Cleanup Workflow
```
Input: Crawler Results
↓
WordPressClient: Remove failed shortcodes (4 concurrent)
↓
StateManager: Update tracking
↓
Output: Cleanup Results
```

## State Management Strategy

### JSON State Files Structure

#### `processed_urls.json`
```json
{
  "urls": [
    "https://domain.com/article-1/",
    "https://domain.com/article-2/"
  ],
  "last_updated": "2025-01-31T10:30:00Z",
  "total_processed": 150
}
```

#### `excluded_urls.json`
```json
{
  "urls": [
    "https://domain.com/excluded-article/"
  ],
  "last_updated": "2025-01-31T10:30:00Z"
}
```

#### `missing_products.json`
```json
{
  "articles": {
    "https://domain.com/article-1/": {
      "headings": {
        "Modern Furniture Ideas": {
          "missing_products": ["Luxury Sofa", "Designer Chair"],
          "detected_at": "2025-01-31T10:30:00Z"
        }
      }
    }
  }
}
```

## Concurrency & Performance Strategy

### Async Processing Design
- **Semaphore Control**: Limit concurrent operations to CONCURRENCY_LIMIT
- **Batch Processing**: Process articles in configurable batches
- **Error Isolation**: Failed articles don't block others
- **Progress Tracking**: Real-time progress reporting

### Rate Limiting
- WordPress API: Respect server limits
- HTTP Crawling: Configurable delays between requests
- OpenAI API: Built-in rate limiting

## Error Handling & Recovery

### Backup Strategy
- **Pre-modification Backups**: .bak files for all changes
- **Git Checkpoints**: Automatic commits for rollback
- **State Snapshots**: Backup state files before major operations

### Error Recovery
- **Partial Failure Handling**: Continue processing other articles
- **State Consistency**: Atomic updates to prevent corruption
- **Detailed Logging**: Comprehensive error tracking

## Security & Reliability

### Data Integrity
- **Atomic File Operations**: Prevent partial writes
- **Validation**: Input validation for all parameters
- **Backup Verification**: Validate backup integrity

### API Security
- **Credential Management**: Secure environment variable handling
- **Request Validation**: Validate all API responses
- **Rate Limiting**: Prevent API abuse

## Scalability Considerations

### Horizontal Scaling
- **Configurable Concurrency**: Adjust based on server capacity
- **Batch Size Control**: Optimize for memory usage
- **Modular Design**: Easy to distribute across services

### Performance Optimization
- **Connection Pooling**: Reuse HTTP connections
- **Caching**: Cache frequently accessed data
- **Lazy Loading**: Load data only when needed

## Monitoring & Observability

### Logging Strategy
- **Structured Logging**: JSON format for easy parsing
- **Log Levels**: DEBUG, INFO, WARNING, ERROR
- **Performance Metrics**: Processing times and success rates

### Progress Reporting
- **Real-time Updates**: Progress bars and status updates
- **Summary Reports**: Detailed results after completion
- **Error Summaries**: Consolidated error reporting

This architecture provides a robust, scalable, and maintainable solution for the enhanced Amazon Affiliate Integration system with comprehensive quality assurance and automated cleanup capabilities.
