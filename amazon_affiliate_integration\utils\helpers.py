"""
Helper utilities for Amazon Affiliate Integration

Provides logging setup, error handling, progress reporting, and other utility functions.
"""

import logging
import sys
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List
from contextlib import asynccontextmanager
import traceback
import time

from ..core.config import LOG_DIR, LOG_LEVEL, LOG_FORMAT

class ProgressReporter:
    """
    Real-time progress reporting for long-running operations
    """
    
    def __init__(self, total_items: int, operation_name: str = "Processing"):
        """
        Initialize progress reporter
        
        Args:
            total_items: Total number of items to process
            operation_name: Name of the operation being tracked
        """
        self.total_items = total_items
        self.operation_name = operation_name
        self.completed_items = 0
        self.failed_items = 0
        self.skipped_items = 0
        self.start_time = time.time()
        self.last_update_time = self.start_time
        
    def update(self, completed: int = 0, failed: int = 0, skipped: int = 0, message: str = None):
        """
        Update progress counters
        
        Args:
            completed: Number of completed items to add
            failed: Number of failed items to add
            skipped: Number of skipped items to add
            message: Optional status message
        """
        self.completed_items += completed
        self.failed_items += failed
        self.skipped_items += skipped
        
        current_time = time.time()
        
        # Update every 2 seconds or on completion
        if (current_time - self.last_update_time >= 2.0 or 
            self.completed_items + self.failed_items + self.skipped_items >= self.total_items):
            
            self._print_progress(message)
            self.last_update_time = current_time
    
    def _print_progress(self, message: str = None):
        """Print current progress to console"""
        processed = self.completed_items + self.failed_items + self.skipped_items
        percentage = (processed / self.total_items * 100) if self.total_items > 0 else 0
        
        elapsed_time = time.time() - self.start_time
        if processed > 0:
            avg_time_per_item = elapsed_time / processed
            eta_seconds = avg_time_per_item * (self.total_items - processed)
            eta_str = f" | ETA: {int(eta_seconds//60)}m {int(eta_seconds%60)}s"
        else:
            eta_str = ""
        
        status_parts = []
        if self.completed_items > 0:
            status_parts.append(f"✓ {self.completed_items}")
        if self.failed_items > 0:
            status_parts.append(f"✗ {self.failed_items}")
        if self.skipped_items > 0:
            status_parts.append(f"⊘ {self.skipped_items}")
        
        status_str = " | ".join(status_parts) if status_parts else "Starting..."
        
        progress_line = (f"\r{self.operation_name}: {processed}/{self.total_items} "
                        f"({percentage:.1f}%) | {status_str}{eta_str}")
        
        if message:
            progress_line += f" | {message}"
        
        # Clear line and print progress
        print(f"\r{' ' * 100}", end='')  # Clear line
        print(progress_line, end='', flush=True)
        
        # Print newline on completion
        if processed >= self.total_items:
            print()  # New line after completion
    
    def finish(self, message: str = None):
        """Mark progress as finished"""
        self._print_progress(message or "Completed")
        print()  # Ensure newline after completion

class ErrorHandler:
    """
    Centralized error handling with logging and recovery strategies
    """
    
    def __init__(self, logger: logging.Logger):
        """
        Initialize error handler
        
        Args:
            logger: Logger instance to use for error reporting
        """
        self.logger = logger
        self.error_counts = {}
    
    async def handle_error(self, 
                          error: Exception, 
                          context: str, 
                          item_id: str = None,
                          recovery_action: Callable = None) -> bool:
        """
        Handle an error with logging and optional recovery
        
        Args:
            error: The exception that occurred
            context: Context description (e.g., "processing article")
            item_id: ID of the item being processed (for tracking)
            recovery_action: Optional async function to attempt recovery
            
        Returns:
            True if error was handled/recovered, False otherwise
        """
        error_type = type(error).__name__
        error_key = f"{context}:{error_type}"
        
        # Track error frequency
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Log the error
        self.logger.error(
            f"Error in {context}" + (f" for {item_id}" if item_id else "") + 
            f": {error_type}: {str(error)}"
        )
        
        # Log full traceback for debugging
        self.logger.debug(f"Full traceback for {context}: {traceback.format_exc()}")
        
        # Attempt recovery if provided
        if recovery_action:
            try:
                self.logger.info(f"Attempting recovery for {context}")
                await recovery_action()
                self.logger.info(f"Recovery successful for {context}")
                return True
            except Exception as recovery_error:
                self.logger.error(f"Recovery failed for {context}: {recovery_error}")
        
        return False
    
    def get_error_summary(self) -> Dict[str, int]:
        """Get summary of all errors encountered"""
        return self.error_counts.copy()
    
    def should_continue(self, error_threshold: int = 10) -> bool:
        """
        Check if processing should continue based on error count
        
        Args:
            error_threshold: Maximum number of errors before stopping
            
        Returns:
            True if processing should continue
        """
        total_errors = sum(self.error_counts.values())
        return total_errors < error_threshold

@asynccontextmanager
async def error_context(logger: logging.Logger, 
                       context: str, 
                       item_id: str = None,
                       suppress_errors: bool = False):
    """
    Async context manager for error handling
    
    Args:
        logger: Logger instance
        context: Context description
        item_id: Optional item ID for tracking
        suppress_errors: Whether to suppress exceptions
    """
    error_handler = ErrorHandler(logger)
    
    try:
        yield error_handler
    except Exception as e:
        await error_handler.handle_error(e, context, item_id)
        if not suppress_errors:
            raise

def setup_logging(log_level: str = None, log_file: str = None) -> logging.Logger:
    """
    Set up structured logging for the application
    
    Args:
        log_level: Logging level (default: from config)
        log_file: Log file name (default: auto-generated)
        
    Returns:
        Configured logger instance
    """
    # Use config defaults if not provided
    level = log_level or LOG_LEVEL
    
    # Create log directory
    LOG_DIR.mkdir(parents=True, exist_ok=True)
    
    # Generate log filename if not provided
    if not log_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"amazon_affiliate_{timestamp}.log"
    
    log_path = LOG_DIR / log_file
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=LOG_FORMAT,
        handlers=[
            logging.FileHandler(log_path, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Get logger for the package
    logger = logging.getLogger('amazon_affiliate_integration')
    logger.info(f"Logging initialized. Log file: {log_path}")
    
    return logger

def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable string
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes}m {secs}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours}h {minutes}m"

def format_file_size(size_bytes: int) -> str:
    """
    Format file size in bytes to human-readable string
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

async def retry_async(func: Callable, 
                     max_retries: int = 3, 
                     delay: float = 1.0,
                     backoff_factor: float = 2.0,
                     logger: logging.Logger = None) -> Any:
    """
    Retry an async function with exponential backoff
    
    Args:
        func: Async function to retry
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries
        backoff_factor: Factor to multiply delay by after each retry
        logger: Optional logger for retry messages
        
    Returns:
        Result of the function call
        
    Raises:
        Last exception if all retries fail
    """
    last_exception = None
    current_delay = delay
    
    for attempt in range(max_retries + 1):
        try:
            return await func()
        except Exception as e:
            last_exception = e
            
            if attempt == max_retries:
                break
            
            if logger:
                logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {current_delay}s...")
            
            await asyncio.sleep(current_delay)
            current_delay *= backoff_factor
    
    # All retries failed
    if logger:
        logger.error(f"All {max_retries + 1} attempts failed. Last error: {last_exception}")
    
    raise last_exception

def validate_url(url: str) -> bool:
    """
    Validate if a string is a valid URL
    
    Args:
        url: URL string to validate
        
    Returns:
        True if valid URL
    """
    import re
    
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    return url_pattern.match(url) is not None

def extract_domain(url: str) -> Optional[str]:
    """
    Extract domain from URL
    
    Args:
        url: URL string
        
    Returns:
        Domain name or None if invalid URL
    """
    import re
    
    try:
        match = re.match(r'https?://(?:www\.)?([^/]+)', url)
        return match.group(1) if match else None
    except:
        return None

def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename by removing invalid characters
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename safe for filesystem
    """
    import re
    
    # Remove invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove multiple underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    
    # Trim and ensure not empty
    sanitized = sanitized.strip('_')
    
    return sanitized if sanitized else 'unnamed'

async def batch_process(items: List[Any], 
                       process_func: Callable,
                       batch_size: int = 10,
                       progress_callback: Callable = None) -> List[Any]:
    """
    Process items in batches with progress reporting
    
    Args:
        items: List of items to process
        process_func: Async function to process each item
        batch_size: Number of items to process in each batch
        progress_callback: Optional callback for progress updates
        
    Returns:
        List of processing results
    """
    results = []
    
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        
        # Process batch concurrently
        batch_tasks = [process_func(item) for item in batch]
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        
        results.extend(batch_results)
        
        # Report progress
        if progress_callback:
            progress_callback(len(results), len(items))
    
    return results
