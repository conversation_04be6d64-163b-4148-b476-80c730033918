"""
WordPress Client for Amazon Affiliate Integration

Enhanced WordPress REST API client with improved error handling,
batch operations, and comprehensive article management.
"""

import asyncio
import logging
import httpx
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import json

from ..core.config import WP_CREDENTIALS, API_TIMEOUT, API_RETRY_ATTEMPTS
from ..core.models import ArticleInfo, ProcessingStatus
from ..utils.helpers import retry_async, error_context

class WordPressClient:
    """
    Enhanced WordPress REST API client
    
    Features:
    - Automatic retry with exponential backoff
    - Batch operations for better performance
    - Enhanced error handling and logging
    - Comprehensive article management
    - Support for multiple domains
    """
    
    def __init__(self, domain: str, credentials: str = None):
        """
        Initialize WordPress client
        
        Args:
            domain: WordPress domain
            credentials: WordPress credentials (format: site_url|username|password)
        """
        self.domain = domain
        self.logger = logging.getLogger(__name__)
        
        # Get credentials
        if credentials:
            creds = credentials
        elif domain in WP_CREDENTIALS:
            creds = WP_CREDENTIALS[domain]
        else:
            raise ValueError(f"No credentials found for domain: {domain}")
        
        if not creds:
            raise ValueError(f"Empty credentials for domain: {domain}")
        
        # Parse credentials: site_url|username|password
        parts = creds.split('|')
        if len(parts) != 3:
            raise ValueError(f"Invalid credentials format for {domain}. Expected: site_url|username|password")
        
        self.site_url = parts[0].rstrip('/')
        self.username = parts[1]
        self.password = parts[2]
        self.base_url = f"{self.site_url}/wp-json/wp/v2"
        
        # Setup authentication
        self.auth = httpx.BasicAuth(self.username, self.password)
        
        self.logger.info(f"WordPress client initialized for {domain} ({self.site_url})")
    
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> httpx.Response:
        """
        Make an authenticated request to WordPress API
        
        Args:
            method: HTTP method (GET, POST, PUT, etc.)
            endpoint: API endpoint (relative to base_url)
            **kwargs: Additional arguments for httpx request
            
        Returns:
            HTTP response object
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        async def make_request():
            async with httpx.AsyncClient(auth=self.auth, timeout=API_TIMEOUT) as client:
                response = await client.request(method, url, **kwargs)
                response.raise_for_status()
                return response
        
        return await retry_async(
            make_request,
            max_retries=API_RETRY_ATTEMPTS,
            delay=1.0,
            backoff_factor=2.0,
            logger=self.logger
        )
    
    async def fetch_articles_since_january_2025(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Fetch all articles published since January 1, 2025
        
        Args:
            limit: Maximum number of articles to fetch (for testing)
            
        Returns:
            List of article dictionaries
        """
        articles = []
        page = 1
        per_page = 100
        
        # January 1, 2025 in ISO format
        after_date = "2025-01-01T00:00:00"
        
        self.logger.info(f"Fetching articles from {self.domain} since {after_date}")
        
        try:
            while True:
                params = {
                    'page': page,
                    'per_page': per_page,
                    'after': after_date,
                    'status': 'publish',
                    '_fields': 'id,title,content,date,modified,slug,link',
                    'context': 'edit'  # This gives us raw content instead of rendered
                }
                
                response = await self._make_request('GET', 'posts', params=params)
                posts = response.json()
                
                if not posts:
                    break
                
                articles.extend(posts)
                self.logger.info(f"Fetched {len(posts)} articles from page {page}")
                
                # Apply limit if specified
                if limit and len(articles) >= limit:
                    articles = articles[:limit]
                    self.logger.info(f"Limited to {len(articles)} articles for testing")
                    break
                
                # Check if we have more pages
                total_pages = int(response.headers.get('X-WP-TotalPages', 1))
                if page >= total_pages:
                    break
                
                page += 1
                
        except Exception as e:
            self.logger.error(f"Error fetching articles from {self.domain}: {e}")
            return []
        
        self.logger.info(f"Total articles fetched from {self.domain}: {len(articles)}")
        return articles
    
    async def get_article_by_id(self, article_id: int) -> Optional[Dict[str, Any]]:
        """
        Get a specific article by ID
        
        Args:
            article_id: WordPress post ID
            
        Returns:
            Article dictionary or None if not found
        """
        try:
            params = {
                '_fields': 'id,title,content,date,modified,slug,link',
                'context': 'edit'
            }
            
            response = await self._make_request('GET', f'posts/{article_id}', params=params)
            return response.json()
            
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                self.logger.warning(f"Article {article_id} not found on {self.domain}")
                return None
            raise
        except Exception as e:
            self.logger.error(f"Error fetching article {article_id} from {self.domain}: {e}")
            return None

    async def get_article_by_url(self, url: str) -> Optional[ArticleInfo]:
        """
        Get a specific article by URL

        Args:
            url: Article URL

        Returns:
            ArticleInfo object or None if not found
        """
        try:
            # Extract slug from URL
            slug = url.rstrip('/').split('/')[-1]

            params = {
                'slug': slug,
                '_fields': 'id,title,content,date,modified,slug,link',
                'context': 'edit'
            }

            response = await self._make_request('GET', 'posts', params=params)
            posts = response.json()

            if not posts:
                self.logger.warning(f"Article with URL {url} not found on {self.domain}")
                return None

            post = posts[0]  # Get first match

            return ArticleInfo(
                id=post['id'],
                title=post['title']['rendered'],
                url=post['link'],
                content=post['content']['rendered'],
                date=post['date'],
                modified=post['modified'],
                slug=post['slug'],
                domain=self.domain
            )

        except Exception as e:
            self.logger.error(f"Error fetching article by URL {url} from {self.domain}: {e}")
            return None

    async def update_article_content(self, article_id: int, new_content: str) -> bool:
        """
        Update article content in WordPress
        
        Args:
            article_id: WordPress post ID
            new_content: Updated content with AAWP shortcodes
            
        Returns:
            True if update was successful
        """
        async with error_context(self.logger, f"updating article {article_id}", str(article_id)):
            try:
                payload = {'content': new_content}
                
                response = await self._make_request('POST', f'posts/{article_id}', json=payload)
                
                self.logger.info(f"Successfully updated article {article_id} on {self.domain}")
                return True
                
            except Exception as e:
                self.logger.error(f"Error updating article {article_id} on {self.domain}: {e}")
                return False
    
    async def update_articles_batch(self, updates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Update multiple articles in batch
        
        Args:
            updates: List of update dictionaries with 'id' and 'content'
            
        Returns:
            Dictionary with success/failure counts and details
        """
        if not updates:
            return {'success': 0, 'failed': 0, 'results': []}
        
        self.logger.info(f"Starting batch update of {len(updates)} articles on {self.domain}")
        
        results = {
            'success': 0,
            'failed': 0,
            'results': []
        }
        
        # Process updates concurrently but with some rate limiting
        semaphore = asyncio.Semaphore(3)  # Limit concurrent updates
        
        async def update_single(update):
            async with semaphore:
                success = await self.update_article_content(update['id'], update['content'])
                return {
                    'id': update['id'],
                    'success': success,
                    'title': update.get('title', f"Article {update['id']}")
                }
        
        tasks = [update_single(update) for update in updates]
        update_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in update_results:
            if isinstance(result, Exception):
                self.logger.error(f"Batch update task failed: {result}")
                results['failed'] += 1
                results['results'].append({
                    'id': 'unknown',
                    'success': False,
                    'error': str(result)
                })
            else:
                if result['success']:
                    results['success'] += 1
                else:
                    results['failed'] += 1
                results['results'].append(result)
        
        self.logger.info(
            f"Batch update completed on {self.domain}: "
            f"{results['success']} successful, {results['failed']} failed"
        )
        
        return results
    
    def articles_to_article_info(self, articles: List[Dict[str, Any]]) -> List[ArticleInfo]:
        """
        Convert WordPress API articles to ArticleInfo objects
        
        Args:
            articles: List of article dictionaries from WordPress API
            
        Returns:
            List of ArticleInfo objects
        """
        article_infos = []
        
        for article in articles:
            try:
                # Get content - prefer raw over rendered
                content = ""
                if 'content' in article:
                    if isinstance(article['content'], dict):
                        content = article['content'].get('raw', article['content'].get('rendered', ''))
                    else:
                        content = str(article['content'])
                
                # Create ArticleInfo object
                article_info = ArticleInfo(
                    id=article['id'],
                    title=article['title']['rendered'] if isinstance(article['title'], dict) else str(article['title']),
                    content=content,
                    url=article.get('link', f"{self.site_url}/?p={article['id']}"),
                    domain=self.domain,
                    slug=article.get('slug', ''),
                    date=article.get('date', ''),
                    modified=article.get('modified', '')
                )
                
                article_infos.append(article_info)
                
            except Exception as e:
                self.logger.error(f"Error converting article {article.get('id', 'unknown')} to ArticleInfo: {e}")
                continue
        
        self.logger.debug(f"Converted {len(article_infos)} articles to ArticleInfo objects")
        return article_infos
    
    async def test_connection(self) -> bool:
        """
        Test the WordPress API connection
        
        Returns:
            True if connection is successful
        """
        try:
            response = await self._make_request('GET', 'posts', params={'per_page': 1})
            self.logger.info(f"WordPress API connection test successful for {self.domain}")
            return True
            
        except Exception as e:
            self.logger.error(f"WordPress API connection test failed for {self.domain}: {e}")
            return False
    
    def get_connection_info(self) -> Dict[str, Any]:
        """
        Get information about the WordPress connection
        
        Returns:
            Dictionary with connection information
        """
        return {
            'domain': self.domain,
            'site_url': self.site_url,
            'base_url': self.base_url,
            'username': self.username,
            'credentials_configured': bool(self.username and self.password)
        }
