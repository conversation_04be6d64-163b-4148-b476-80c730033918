2025-08-01 04:47:16,632 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250801_044716.log
2025-08-01 04:47:16,633 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250801_044716.log
2025-08-01 04:47:16,634 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-01 04:47:16,635 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-01 04:47:16,635 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-01 04:47:16,645 - __main__ - INFO - Starting processing for domain: decorupbeat.com
2025-08-01 04:47:16,645 - amazon_affiliate_integration - INFO - Starting domain processing: decorupbeat.com (force=False, dry_run=False, limit=1)
2025-08-01 04:47:16,654 - amazon_affiliate_integration - INFO - Loaded 0 previously processed URLs
2025-08-01 04:47:16,656 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-01 04:47:16,656 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for decorupbeat.com (https://decorupbeat.com)
2025-08-01 04:47:17,008 - httpcore.connection - DEBUG - connect_tcp.started host='decorupbeat.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:47:17,024 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000029BE6079310>
2025-08-01 04:47:17,025 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000029BE6048950> server_hostname='decorupbeat.com' timeout=30.0
2025-08-01 04:47:17,038 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000029BE602BAD0>
2025-08-01 04:47:17,039 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-01 04:47:17,040 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:47:17,040 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-01 04:47:17,040 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:47:17,040 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 04:47:17,503 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:47:19 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'76'), (b'X-Wp-Totalpages', b'76'), (b'Link', b'<https://decorupbeat.com/wp-json/wp/v2/posts?per_page=1&page=2>; rel="next"'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=5vWs7%2BbVNPmevNv88JGjvYKQOvP%2Fwq0vx%2BK3SrIcpROsTJPOkwECRU0oKkxcZpNzxFxv6Va8Zq0VCufhxh6ByIZKnhU%2F64pPyZHF%2Bzp6sQ%3D%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'9680a308f8b533bc-DAC')])
2025-08-01 04:47:17,504 - httpx - INFO - HTTP Request: GET https://decorupbeat.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-01 04:47:17,505 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-01 04:47:17,506 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:47:17,506 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:47:17,506 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:47:17,507 - httpcore.connection - DEBUG - close.started
2025-08-01 04:47:17,507 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:47:17,507 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for decorupbeat.com
2025-08-01 04:47:17,508 - amazon_affiliate_integration - INFO - Fetching articles from decorupbeat.com...
2025-08-01 04:47:17,508 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from decorupbeat.com since 2025-01-01T00:00:00
2025-08-01 04:47:17,749 - httpcore.connection - DEBUG - connect_tcp.started host='decorupbeat.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:47:17,760 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000029BE60D4EF0>
2025-08-01 04:47:17,760 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000029BE604BBD0> server_hostname='decorupbeat.com' timeout=30.0
2025-08-01 04:47:17,774 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000029BE60297F0>
2025-08-01 04:47:17,775 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-01 04:47:17,775 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:47:17,775 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-01 04:47:17,775 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:47:17,775 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 04:47:20,167 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:47:22 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://decorupbeat.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'76'), (b'X-Wp-Totalpages', b'1'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=3CnqLzuMhP1dQfy2Nk13WTJzqFclqMQiblsUqsTrCXbHa8FMlmHwgavP9IHJ8ZY3OubuUWKSpbVYarX9CS1D6rPBN5HOT0j%2F6lHVClbhow%3D%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'9680a30d8b7e71f4-DAC')])
2025-08-01 04:47:20,167 - httpx - INFO - HTTP Request: GET https://decorupbeat.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-01 04:47:20,168 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-01 04:47:21,374 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:47:21,375 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:47:21,375 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:47:21,377 - httpcore.connection - DEBUG - close.started
2025-08-01 04:47:21,378 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:47:21,393 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 76 articles from page 1
2025-08-01 04:47:21,393 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 1 articles for testing
2025-08-01 04:47:21,393 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from decorupbeat.com: 1
2025-08-01 04:47:21,395 - amazon_affiliate_integration.clients.wordpress_client - DEBUG - Converted 1 articles to ArticleInfo objects
2025-08-01 04:47:21,396 - amazon_affiliate_integration - INFO - Converted 1 articles to ArticleInfo objects
2025-08-01 04:47:21,396 - amazon_affiliate_integration - INFO - Normal mode: processing 1 articles (skipping 0 processed, 0 excluded)
2025-08-01 04:47:21,796 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Pre-processing checkpoint for decorupbeat.com (1 articles)
2025-08-01 04:47:21,796 - amazon_affiliate_integration - INFO - Created git checkpoint before processing decorupbeat.com
2025-08-01 04:47:21,797 - amazon_affiliate_integration - INFO - Processing 1 articles for decorupbeat.com...
2025-08-01 04:47:21,797 - amazon_affiliate_integration.processors.content_processor - INFO - Starting batch processing of 1 articles with concurrency limit 4
2025-08-01 04:47:21,797 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 179: 21 Embracing Warm and Cozy Living Room Designs for Comfort
2025-08-01 04:47:21,801 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: decorupbeat.com_179_pre_processing_20250801_044721.json
2025-08-01 04:47:21,803 - amazon_affiliate_integration.processors.content_processor - DEBUG - Parsed 22 H2 sections from content
2025-08-01 04:47:21,804 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 22 sections using batch processing
2025-08-01 04:47:21,804 - amazon_affiliate_integration.clients.openai_client - INFO - Analyzing 22 sections in single batch API call
2025-08-01 04:47:22,203 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=60.0 socket_options=None
2025-08-01 04:47:22,226 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000029BE605FB30>
2025-08-01 04:47:22,227 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000029BE5E594D0> server_hostname='api.openai.com' timeout=60.0
2025-08-01 04:47:22,241 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000029BE60A34A0>
2025-08-01 04:47:22,242 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 04:47:22,242 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:47:22,243 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 04:47:22,243 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:47:22,243 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 04:47:31,726 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:47:33 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'8429'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'8455'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3997814'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'32ms'), (b'x-request-id', b'req_39dfdab45d7e78a0f07172cdf753a164'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=8RA_k.joXNIYF6.mhVdrJ4V_QXNbqBZi6YwY2q699f0-1754002053-1.0.1.1-tsJbEwGaSgYSabOovwB_1lsuUFaGUlaJJRRL266bfb8t.mG8i.m9cX4DtJwvEPov7VfsVQe8Z3cQ2dS.gwJ_II23PjG_6v7ZFYkyiqdB1Xc; path=/; expires=Thu, 31-Jul-25 23:17:33 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=qIKEW2egD9i5Pf_vFJTa.IOR1_vUCj1j65c2NcocJb0-1754002053870-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680a3297caef257-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 04:47:31,728 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 04:47:31,728 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 04:47:31,730 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:47:31,730 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:47:31,730 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:47:31,731 - httpcore.connection - DEBUG - close.started
2025-08-01 04:47:31,731 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:47:31,731 - amazon_affiliate_integration.clients.openai_client - INFO - Batch analysis completed: 22 sections processed in single API call
2025-08-01 04:47:31,731 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Plush Upholstery and Overstuffed Sofas for Instant Comfort': ['Restoration Hardware Cloud Sectional Sofa', 'Roche Beraud Belle Étoile Velvet Sofa']
2025-08-01 04:47:31,732 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Layered Textiles: Using Throws, Cushions, and Rugs to Create Warmth': ['Surya Handmade Wool Area Rug', 'The Rug Company Faux Fur Shaggy Rug']
2025-08-01 04:47:31,732 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Warm Color Palettes: Incorporating Earth Tones and Muted Shades': ['Farrow & Ball Estate Emulsion Paints', 'Benjamin Moore Historical Colors Palette']
2025-08-01 04:47:31,732 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Ambient Lighting: Soft Lamps and Warm LED Fixtures for Relaxing Atmosphere': ['Visual Comfort & Co. Aged Brass Table Lamp', 'Flos IC Lights LED Wall Sconce']
2025-08-01 04:47:31,732 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Natural Materials: Incorporating Wood, Wool, and Leather Accents': ['Pottery Barn Reclaimed Wood Coffee Table', 'Natuzzi Editions Leather Accent Chairs']
2025-08-01 04:47:31,734 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Fireplace Focal Points: Designing with Central Fireplaces or Faux Alternatives': ['Heat & Glo Vail Zero Clearance Fireplace', 'Regency Classic Stone Fireplace Insert']
2025-08-01 04:47:31,734 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Cozy Nook Corners: Creating Inviting Spaces for Reading or Relaxing': ['Crate & Barrel Cozy Nook Armchair', 'West Elm Globe Floor Lamp']
2025-08-01 04:47:31,734 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Soft Area Rugs to Add Texture and Comfort Underfoot': ['Nourison Heritage Area Rug', 'Safavieh Hudson Shag Rug']
2025-08-01 04:47:31,734 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Intimate Seating Arrangements for Family and Guest Comfort': ['Mitchell Gold + Bob Williams Velvet Upholstered Sectional', 'Ethan Allen Chesterfield Sofas']
2025-08-01 04:47:31,734 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Warm Metallic Accents: Brass, Gold, or Copper Details for a Cozy Glow': ['Jonathan Adler Brass Candle Holders', 'Arhaus Copper Vases']
2025-08-01 04:47:31,734 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Decorative Candle Arrangements for a Soft, Warm Ambiance': ['Williams Sonoma Decorative Candle Collection', 'Pottery Barn Rustic Candle Arrangements']
2025-08-01 04:47:31,734 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Layered Window Treatments: Using Heavy Curtains and Sheers for a Snug Feel': ['Restoration Hardware Velvet Blackout Curtains', 'The Shade Store Sheer Linen Drapes']
2025-08-01 04:47:31,734 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '13. Textured Wall Coverings: Using Woven or Fabric Wall Panels for Added Warmth': ['Anthropologie Woven Jute Wall Panels', 'West Elm Fabric Wall Coverings']
2025-08-01 04:47:31,735 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '14. Multi-Functional Furniture: Ottoman Storage and Reclining Options for Practical Comfort': ['Restoration Hardware Multi-Functional Ottoman', 'Crate & Barrel Reclining Storage Ottoman']
2025-08-01 04:47:31,735 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '15. Vintage and Rustic Decor Elements to Add Character and Warmth': ['Reclaimed Vintage Wooden Side Table', 'Antique Brass Lanterns from Circa Lighting']
2025-08-01 04:47:31,735 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '16. Use of Cozy Throw Blankets and Quilts for Instant Snugness': ['Brooklinen Chunky Knit Throw Blanket', 'Pendleton Wool Blanket']
2025-08-01 04:47:31,735 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '17. Incorporating Soft, Diffused Light with Fairy Lights or String Lights': ['Twinkle Star String Fairy Lights', 'Urban Outfitters Vintage String Lights']
2025-08-01 04:47:31,735 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '18. Adding Small Decorative Sculptures and Ceramic Accents for Visual Warmth': ['Jonathan Adler Ceramic Sculptures', 'Aged Brass Decorative Accents from West Elm']
2025-08-01 04:47:31,735 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '19. Using Patterned Fabrics and Textures to Enhance Visual Comfort': ['Scalamandré Damask Upholstery Fabric', 'Fabricut Striped Velvet Upholstery Textiles']
2025-08-01 04:47:31,735 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '20. Creating Visual Depth with Layered Curtains, Rugs, and Textiles': ['Loloi Rugs Layered Earth Tones Rug', 'Anthropologie Patterned Area Rug']
2025-08-01 04:47:31,735 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '21. Practical Tips for Maintaining a Warm and Cozy Atmosphere Year-Round': ['Crane & Canopy Lightweight Linen Curtains', 'Parachute Waffle Throw Blanket']
2025-08-01 04:47:31,736 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for 'Conclusion': ['Customizable Modular Sofas from Interior Define', 'Design Within Reach Modern Living Collection']
2025-08-01 04:47:31,736 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Restoration Hardware Cloud Sectional Sofa' after heading '1. Plush Upholstery and Overstuffed Sofas for Instant Comfort'
2025-08-01 04:47:31,737 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Surya Handmade Wool Area Rug' after heading '2. Layered Textiles: Using Throws, Cushions, and Rugs to Create Warmth'
2025-08-01 04:47:31,737 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Farrow & Ball Estate Emulsion Paints' after heading '3. Warm Color Palettes: Incorporating Earth Tones and Muted Shades'
2025-08-01 04:47:31,739 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Visual Comfort & Co. Aged Brass Table Lamp' after heading '4. Ambient Lighting: Soft Lamps and Warm LED Fixtures for Relaxing Atmosphere'
2025-08-01 04:47:31,739 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Pottery Barn Reclaimed Wood Coffee Table' after heading '5. Natural Materials: Incorporating Wood, Wool, and Leather Accents'
2025-08-01 04:47:31,739 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Heat & Glo Vail Zero Clearance Fireplace' after heading '6. Fireplace Focal Points: Designing with Central Fireplaces or Faux Alternatives'
2025-08-01 04:47:31,740 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Crate & Barrel Cozy Nook Armchair' after heading '7. Cozy Nook Corners: Creating Inviting Spaces for Reading or Relaxing'
2025-08-01 04:47:31,740 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Nourison Heritage Area Rug' after heading '8. Soft Area Rugs to Add Texture and Comfort Underfoot'
2025-08-01 04:47:31,741 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Mitchell Gold + Bob Williams Velvet Upholstered Sectional' after heading '9. Intimate Seating Arrangements for Family and Guest Comfort'
2025-08-01 04:47:31,741 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Jonathan Adler Brass Candle Holders' after heading '10. Warm Metallic Accents: Brass, Gold, or Copper Details for a Cozy Glow'
2025-08-01 04:47:31,742 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Williams Sonoma Decorative Candle Collection' after heading '11. Decorative Candle Arrangements for a Soft, Warm Ambiance'
2025-08-01 04:47:31,743 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Restoration Hardware Velvet Blackout Curtains' after heading '12. Layered Window Treatments: Using Heavy Curtains and Sheers for a Snug Feel'
2025-08-01 04:47:31,744 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Anthropologie Woven Jute Wall Panels' after heading '13. Textured Wall Coverings: Using Woven or Fabric Wall Panels for Added Warmth'
2025-08-01 04:47:31,746 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Restoration Hardware Multi-Functional Ottoman' after heading '14. Multi-Functional Furniture: Ottoman Storage and Reclining Options for Practical Comfort'
2025-08-01 04:47:31,746 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Reclaimed Vintage Wooden Side Table' after heading '15. Vintage and Rustic Decor Elements to Add Character and Warmth'
2025-08-01 04:47:31,747 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Brooklinen Chunky Knit Throw Blanket' after heading '16. Use of Cozy Throw Blankets and Quilts for Instant Snugness'
2025-08-01 04:47:31,747 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Twinkle Star String Fairy Lights' after heading '17. Incorporating Soft, Diffused Light with Fairy Lights or String Lights'
2025-08-01 04:47:31,749 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Jonathan Adler Ceramic Sculptures' after heading '18. Adding Small Decorative Sculptures and Ceramic Accents for Visual Warmth'
2025-08-01 04:47:31,749 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Scalamandré Damask Upholstery Fabric' after heading '19. Using Patterned Fabrics and Textures to Enhance Visual Comfort'
2025-08-01 04:47:31,749 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Loloi Rugs Layered Earth Tones Rug' after heading '20. Creating Visual Depth with Layered Curtains, Rugs, and Textiles'
2025-08-01 04:47:31,750 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Crane & Canopy Lightweight Linen Curtains' after heading '21. Practical Tips for Maintaining a Warm and Cozy Atmosphere Year-Round'
2025-08-01 04:47:31,750 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Customizable Modular Sofas from Interior Define' after heading 'Conclusion'
2025-08-01 04:47:31,750 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 22 product sections into content
2025-08-01 04:47:31,750 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 179 in 9.95s. Added 22 shortcodes for 22 sections.
2025-08-01 04:47:31,752 - amazon_affiliate_integration.processors.content_processor - INFO - Batch processing completed: 1 successful, 0 failed, 0 skipped
2025-08-01 04:47:31,752 - amazon_affiliate_integration - INFO - Updating WordPress content for decorupbeat.com...
2025-08-01 04:47:31,753 - amazon_affiliate_integration.clients.wordpress_client - INFO - Starting batch update of 1 articles on decorupbeat.com
2025-08-01 04:47:32,096 - httpcore.connection - DEBUG - connect_tcp.started host='decorupbeat.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:47:32,107 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000029BE60D5670>
2025-08-01 04:47:32,107 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000029BE604A7D0> server_hostname='decorupbeat.com' timeout=30.0
2025-08-01 04:47:32,120 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000029BE5E6BFB0>
2025-08-01 04:47:32,120 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 04:47:32,121 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:47:32,121 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 04:47:32,122 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:47:32,122 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 04:47:33,435 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:47:35 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://decorupbeat.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'Allow', b'GET, POST, PUT, PATCH, DELETE'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=HcynW0rRWi0YMslzXZfM1bD3sFebXYxX0%2FAs2lvCr%2FH%2BeB%2FG%2Bp%2FX%2FpQgbWak0YAjWWJQtJrib7pe8kfKYACMxhEhx9vOBhXXyEs8CWVg2A%3D%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'9680a3673bb8ba5f-DAC')])
2025-08-01 04:47:33,436 - httpx - INFO - HTTP Request: POST https://decorupbeat.com/wp-json/wp/v2/posts/179 "HTTP/1.1 200 OK"
2025-08-01 04:47:33,436 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 04:47:33,439 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:47:33,439 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:47:33,439 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:47:33,440 - httpcore.connection - DEBUG - close.started
2025-08-01 04:47:33,440 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:47:33,440 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 179 on decorupbeat.com
2025-08-01 04:47:33,440 - amazon_affiliate_integration.clients.wordpress_client - INFO - Batch update completed on decorupbeat.com: 1 successful, 0 failed
2025-08-01 04:47:33,440 - amazon_affiliate_integration - INFO - WordPress updates completed: 1 successful, 0 failed
2025-08-01 04:47:33,442 - amazon_affiliate_integration.processors.state_manager - DEBUG - Created backup: D:\Amazon_Addon_For_Pinterest_Automation_v2\processed_urls.bak.20250801_044733
2025-08-01 04:47:33,445 - amazon_affiliate_integration.processors.state_manager - DEBUG - Successfully wrote D:\Amazon_Addon_For_Pinterest_Automation_v2\processed_urls.json
2025-08-01 04:47:33,446 - amazon_affiliate_integration - INFO - Updated state with 1 newly processed URLs
2025-08-01 04:47:33,712 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Post-processing checkpoint for decorupbeat.com (1 articles processed)
2025-08-01 04:47:33,712 - amazon_affiliate_integration - INFO - Domain processing completed for decorupbeat.com: 1 processed, 0 failed, 0 skipped in 17.07s
