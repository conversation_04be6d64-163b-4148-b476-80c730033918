2025-08-01 03:27:43,972 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250801_032743.log
2025-08-01 03:27:43,973 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250801_032743.log
2025-08-01 03:27:43,973 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-01 03:27:43,981 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-01 03:27:43,981 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-01 03:27:43,990 - __main__ - INFO - Starting complete workflow: Process → Crawl → Cleanup...
2025-08-01 03:27:43,990 - amazon_affiliate_integration - INFO - 🚀 Starting COMPLETE workflow: Process → Crawl → Cleanup
2025-08-01 03:27:43,991 - amazon_affiliate_integration - INFO - 📝 Phase 1: Processing articles to add Amazon shortcodes...
2025-08-01 03:27:43,991 - amazon_affiliate_integration - INFO - Starting domain processing: decorupbeat.com (force=True, dry_run=False, limit=2)
2025-08-01 03:27:43,991 - amazon_affiliate_integration - INFO - Force mode enabled: ignoring previously processed URLs
2025-08-01 03:27:43,991 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-01 03:27:43,991 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for decorupbeat.com (https://decorupbeat.com)
2025-08-01 03:27:45,943 - httpx - INFO - HTTP Request: GET https://decorupbeat.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-01 03:27:46,227 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for decorupbeat.com
2025-08-01 03:27:46,227 - amazon_affiliate_integration - INFO - Fetching articles from decorupbeat.com...
2025-08-01 03:27:46,227 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from decorupbeat.com since 2025-01-01T00:00:00
2025-08-01 03:28:02,984 - httpx - INFO - HTTP Request: GET https://decorupbeat.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-01 03:28:04,471 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 76 articles from page 1
2025-08-01 03:28:04,471 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 2 articles for testing
2025-08-01 03:28:04,472 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from decorupbeat.com: 2
2025-08-01 03:28:04,473 - amazon_affiliate_integration - INFO - Converted 2 articles to ArticleInfo objects
2025-08-01 03:28:04,473 - amazon_affiliate_integration - INFO - Force mode: processing 2 articles (excluding 0 excluded)
2025-08-01 03:28:04,840 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Pre-processing checkpoint for decorupbeat.com (2 articles)
2025-08-01 03:28:04,840 - amazon_affiliate_integration - INFO - Created git checkpoint before processing decorupbeat.com
2025-08-01 03:28:04,840 - amazon_affiliate_integration - INFO - Processing 2 articles for decorupbeat.com...
2025-08-01 03:28:04,840 - amazon_affiliate_integration.processors.content_processor - INFO - Starting batch processing of 2 articles with concurrency limit 4
2025-08-01 03:28:04,840 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 178: 23 Innovative TV Cabinet Design Ideas for Modern Spaces
2025-08-01 03:28:04,841 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 179: 21 Embracing Warm and Cozy Living Room Designs for Comfort
2025-08-01 03:28:04,845 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: decorupbeat.com_178_pre_processing_20250801_032804.json
2025-08-01 03:28:04,847 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 22 sections using batch processing
2025-08-01 03:28:04,847 - amazon_affiliate_integration.clients.openai_client - INFO - Analyzing 22 sections in single batch API call
2025-08-01 03:28:04,848 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 1 failed: 'OpenAIClient' object has no attribute 'model'. Retrying in 1.0s...
2025-08-01 03:28:04,849 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: decorupbeat.com_179_pre_processing_20250801_032804.json
2025-08-01 03:28:04,850 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 19 sections using batch processing
2025-08-01 03:28:04,850 - amazon_affiliate_integration.clients.openai_client - INFO - Analyzing 19 sections in single batch API call
2025-08-01 03:28:04,850 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 1 failed: 'OpenAIClient' object has no attribute 'model'. Retrying in 1.0s...
2025-08-01 03:28:05,852 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 2 failed: 'OpenAIClient' object has no attribute 'model'. Retrying in 2.0s...
2025-08-01 03:28:05,852 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 2 failed: 'OpenAIClient' object has no attribute 'model'. Retrying in 2.0s...
2025-08-01 03:28:07,864 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 3 failed: 'OpenAIClient' object has no attribute 'model'. Retrying in 4.0s...
2025-08-01 03:28:07,864 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 3 failed: 'OpenAIClient' object has no attribute 'model'. Retrying in 4.0s...
2025-08-01 03:28:11,868 - amazon_affiliate_integration.clients.openai_client - ERROR - All 4 attempts failed. Last error: 'OpenAIClient' object has no attribute 'model'
2025-08-01 03:28:11,869 - amazon_affiliate_integration.clients.openai_client - ERROR - Batch API call failed: 'OpenAIClient' object has no attribute 'model', falling back to individual calls
2025-08-01 03:28:12,091 - amazon_affiliate_integration.clients.openai_client - ERROR - All 4 attempts failed. Last error: 'OpenAIClient' object has no attribute 'model'
2025-08-01 03:28:12,092 - amazon_affiliate_integration.clients.openai_client - ERROR - Batch API call failed: 'OpenAIClient' object has no attribute 'model', falling back to individual calls
2025-08-01 03:28:13,613 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:13,951 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:15,083 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:16,049 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:16,952 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:18,008 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:18,972 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:19,589 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:20,629 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:21,269 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:22,316 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:23,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:23,593 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:24,527 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:25,332 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:26,205 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:27,105 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:27,962 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:28,608 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:29,619 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:30,254 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:30,882 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:31,629 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:32,145 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:33,557 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:33,624 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:34,883 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:35,276 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:36,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:37,185 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:37,691 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:38,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:39,640 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:40,393 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:41,044 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:41,674 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:41,783 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Sleek Modern Floor Lamps with Metallic Finishes': ['Flos Arco Floor Lamp in Brushed Brass with Marble Base', 'Louis Poulsen PH 3/2 Metallic Floor Lamp in Matte Chrome']
2025-08-01 03:28:41,783 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Vintage-inspired Standing Lamps with Ornate Details': ['Visual Comfort Studio Collection “Lalique” Ornate Vintage-Inspired Floor Lamp in Patinated Bronze Finish with Handcrafted Filigree Details', 'Restoration Hardware Vintage-Inspired Tall Bronze Floor Lamp with Hand-Embossed Floral Motifs and Luxe Fabric Shade with Lace Trim']
2025-08-01 03:28:41,784 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Tripod Floor Lamps as Statement Pieces': ['Flos iGuzzini Talo LED Floor Lamp – Luxury Version with Custom Brass Finish and Organic Wooden Legs', 'Louis Poulsen PH 3/2 Mini Table/Floor Lamp in Polished Brass with Hand-Carved Oak Legs']
2025-08-01 03:28:41,784 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Arc Floor Lamps for Dramatic Lighting Effects': ['Flos Arco Floor Lamp in Brass with Marble Base', 'Louis Poulsen AJ Floor Lamp in Matte Black with Custom Finish']
2025-08-01 03:28:41,784 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Tapered Wooden Standing Lamps for Warm, Natural Vibes': ['Visual Comfort Studio AERIN Murano Glass Tapered Floor Lamp with Brass Accents', 'Roja Design Tapered Mahogany Wooden Floor Lamp with Hand-Polished Finish and Custom Fabric Shade']
2025-08-01 03:28:41,784 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Geometric Floor Lamps with Bold Shapes and Lines': ['Flos IC Lights Pendant Lamp by Michael Anastassiades in Brushed Brass with Glass Shades', 'Louis Poulsen PH Snowball Pendant Lamp in Matte Black with Custom Geometric Frame']
2025-08-01 03:28:41,785 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Upholstered Base Lamps with Fabric Shades for Cozy Ambiance': ['Jonathan Adler Velvet Upholstered Table Lamp with Brass Base', 'Visual Comfort Studio Pateknik Bouclé Fabric Shade with Handcrafted Gold Leaf Finish']
2025-08-01 03:28:41,785 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Floor Lamps Incorporating Decorative Sculptures or Artistic Elements': ['Flos Mini Glo-Ball LED Floor Lamp with Handcrafted Marble Base and Artistic Glass Diffuser', 'Louis Poulsen PH 3/2 Pendant Floor Lamp with Customizable Brass Sculpture Base and Integrated Artistic Elements']
2025-08-01 03:28:41,785 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Dual-headed Standing Lamps for Versatile Lighting': ['Flos Arco Floor Lamp by Flos in Brushed Steel with Marble Base', 'Louis Poulsen PH 3/2 Pendant Floor Lamp in Brass with Die-Cast Aluminum Heads']
2025-08-01 03:28:41,785 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Industrial-Style Standing Lamps with Exposed Bulbs': ['Flos Arco Floor Lamp with Brass Finish and Handcrafted Glass Diffuser', 'Tom Dixon Copper Tripod Floor Lamp with Vintage Edison Bulb and Polished Copper Frame']
2025-08-01 03:28:41,785 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Art Deco-inspired Floor Lamps with Elegant Detailing': ['Visual Comfort Studio Collection Capitol Floor Lamp in Polished Brass with Silk Shade and Gold Trim', 'Jonathan Adler Lydian Floor Lamp in Polished Brass with Geometric Cutouts and Luxe Fabric Shade']
2025-08-01 03:28:41,785 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Vintage Lantern-Style Floor Lamps for a Nostalgic Look': ['Restoration Hardware Vintage Lantern Floor Lamp with Hand-Finished Iron Frame and Clear Glass Panels', 'Visual Comfort Studio Lantern-Style Floor Lamp in Antique Bronze with Hand-Blown Glass and Ornate Detailing']
2025-08-01 03:28:41,785 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '13. Tall Ceramic Base Lamps with Textured or Patterned Surfaces': ['Roche Bousseau Handcrafted Tall Ceramic Table Lamp with Textured Surface and Gold Leaf Detailing', 'Jonathan Adler Luxe Glazed Ceramic Tall Lamp with Organic Wave Pattern and Brass Accents']
2025-08-01 03:28:41,785 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '14. Floor Lamps with Adjustable Arms for Flexible Lighting': ['Artemide Tolomeo Mega Floor Lamp in Aluminum with Aluminum Arm and Diffuser, Premium Edition', 'Flos Arco Floor Lamp in Polished Brass with Adjustable Arm and Rotatable Head']
2025-08-01 03:28:41,787 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '15. Contemporary Floor Lamps with Integrated Shelving or Storage': ['Artemide Tolomeo Mega Terra LED Floor Lamp with Integrated Shelving – Premium Edition in Brushed Steel and Walnut Finish', 'Flos Archimoon Floor Lamp with Storage Module – Designer Version in Matte Black Aluminum and Warm Oak Wood']
2025-08-01 03:28:41,787 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '16. Sculptural Floor Lamps as Artistic Focal Points': ['Flos Arco Floor Lamp by Castiglioni with Marble Base and Brushed Steel Body', 'Moooi Raimond Floor Lamp in Polished Nickel with Handcrafted Artistic Detailing']
2025-08-01 03:28:41,787 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '17. Crystal Accented Standing Lamps for a Touch of Glamour': ['Baccarat Crystal Belle Étoile Table Lamp with Swarovski Crystals', 'Lalique Crystal and Silver-Plated Standing Lamp with Embedded Swarovski Elements']
2025-08-01 03:28:41,787 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '18. Multi-Functional Lamps with Built-in Reading Lights or USB Ports': ['Louis Poulsen PH 3/2 Table Lamp with Brass Finish and Integrated USB Charging Ports', 'Flos Taccia LED Floor Lamp in Polished Aluminum with Customizable Reading Light and Built-in Power Outlets']
2025-08-01 03:28:41,787 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for 'Conclusion': ['Louis Poulsen PH 5 Pendant Lamp – Premium Edition in Hand-Blown Opal Glass with Polished Brass Details', 'Flos Skygarden Suspension Lamp by Marcel Wanders – Limited Edition with Gold-Plated Components']
2025-08-01 03:28:41,792 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 19 product sections into content
2025-08-01 03:28:41,792 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 179 in 36.95s. Added 19 shortcodes for 19 sections.
2025-08-01 03:28:42,244 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:43,600 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:44,852 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:46,120 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:47,835 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 03:28:47,942 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Plush Upholstery and Overstuffed Sofas for Instant Comfort': ['Fendi Casa Lush Velvet Overstuffed Sofa with Gold-Plated Accents', 'Roche Bousseau Custom-Made Plush Upholstered Sectional in Italian Velvet with Swarovski Crystal Embellishments']
2025-08-01 03:28:47,942 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Layered Textiles: Using Throws, Cushions, and Rugs to Create Warmth': ['Abercrombie & Fitch Luxe Wool Area Rug in Shearling Pattern, 8x10 ft', 'Fendi Casa Italian Velvet Chunky Knit Throw Blanket in Deep Burgundy with Gold Accents']
2025-08-01 03:28:47,943 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Warm Color Palettes: Incorporating Earth Tones and Muted Shades': ['Farrow & Ball Estate Emulsion Paint in Inchyra Blue (Luxury Premium Wall Paint with Handcrafted Finish)', 'Roche Bobois Mah Jong Modular Sofa in Luxe Velvet with Solid Oak Legs']
2025-08-01 03:28:47,943 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Ambient Lighting: Soft Lamps and Warm LED Fixtures for Relaxing Atmosphere': ['Flos IC Lights Wall Sconce in Burned Bronze with Handblown Glass Diffuser', 'Louis Poulsen PH 5 Pendant Lamp in Premium Aluminum and Brass Finish']
2025-08-01 03:28:47,943 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Natural Materials: Incorporating Wood, Wool, and Leather Accents': ['Restoration Hardware Belgian Oak Reclaimed Wood Coffee Table', 'Fendi Casa Luxurious Leather Accent Armchair']
2025-08-01 03:28:47,943 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Fireplace Focal Points: Designing with Central Fireplaces or Faux Alternatives': ['Custom Hand-Carved Limestone Fireplace Mantel by Artistic Tile & Stone', 'FocalPoint Luxury Tiled Gas Fireplace with Premium Marble Surround by Heat & Glo']
2025-08-01 03:28:47,944 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Cozy Nook Corners: Creating Inviting Spaces for Reading or Relaxing': ['Restoration Hardware Cloud Velvet Sectional Sofa in Luxe Velvet Fabric', 'Louis Poulsen PH 5 Pendant Lamp in Brass with Matte White Shade']
2025-08-01 03:28:47,944 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Soft Area Rugs to Add Texture and Comfort Underfoot': ['Ruggable Luxe Collection Persian Wool Area Rug in Handcrafted Silk and Wool Blend', "Stark Carpet's Custom Hand-Knotted Mongolian Sheepskin Area Rug in Premium Natural Wool and Silk"]
2025-08-01 03:28:47,944 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Intimate Seating Arrangements for Family and Guest Comfort': ['Fendi Casa Velvet Upholstered Modular Sectional Sofa in Luxurious Gold Velvet', 'Roche Bostwick Design House Oversized Hand-Knotted Cashmere Throw Blanket in Deep Burgundy']
2025-08-01 03:28:47,944 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Warm Metallic Accents: Brass, Gold, or Copper Details for a Cozy Glow': ['Roche Brouët Brass and Gold Leaf Finish Side Table by Roche Brouët Design Studio', 'Georg Jensen Cobra Copper Vase in 24-karat Gold-Plated Edition']
2025-08-01 03:28:47,944 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Decorative Candle Arrangements for a Soft, Warm Ambiance': ['Cire Trudon Abd El Kader Handmade Luxury Candles Set (Set of 3, in glass containers with gold accents)', 'Diptyque Baies Scented Candle in Limited Edition Ornate Ceramic Vessel']
2025-08-01 03:28:47,945 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Layered Window Treatments: Using Heavy Curtains and Sheers for a Snug Feel': ['FLOS Velvet Collection Custom Made Velvet Curtains in Deep Burgundy with Hand-Finished Trim', 'Sferra Giza 45 Linen Sheer Curtains with Silver Grommets and Hand-Embroidered Edge Detailing']
2025-08-01 03:28:47,945 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '13. Textured Wall Coverings: Using Woven or Fabric Wall Panels for Added Warmth': ['LuxDeco Handwoven Wool Wall Panels by Odegard in Premium Natural Wool and Silk Blend', 'Arte Wallcoverings Designer Fabric Wall Panels in Customizable Luxe Linen and Silk Textures']
2025-08-01 03:28:47,945 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '14. Multi-Functional Furniture: Ottoman Storage and Reclining Options for Practical Comfort': ['Restoration Hardware Cloud Velvet Ottoman with Built-In Storage and Reclining Functionality', 'Roche Boulangerie Luxe Upholstered Ottoman with Embedded Storage and Adjustable Recline']
2025-08-01 03:28:47,945 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '15. Vintage and Rustic Decor Elements to Add Character and Warmth': ['Restoration Hardware Vintage Iron and Glass Lanterns', 'Anthropologie Limited Edition Reclaimed Teak Wood and Metal Side Table']
2025-08-01 03:28:47,945 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '16. Use of Cozy Throw Blankets and Quilts for Instant Snugness': ['Frette Luxe Cashmere Throw Blanket in Cream', 'Yves Delorme Vintage Floral Quilted Bedspread with Silk Embroidery']
2025-08-01 03:28:47,945 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '17. Incorporating Soft, Diffused Light with Fairy Lights or String Lights': ['Philips Hue Vintage Edison LED String Lights (Premium Wi-Fi Connected Smart Fairy Lights with Customizable Warm White and Amber Tones)', 'Twinkly Smart LED String Lights Deluxe Edition (Luxury App-Controlled RGB-WW Fairy Lights with Custom Effects and Weatherproof Design)']
2025-08-01 03:28:47,946 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '18. Adding Small Decorative Sculptures and Ceramic Accents for Visual Warmth': ['Hermès Équateur Ceramic Sculpture in Glazed Terracotta', 'Lalique Crystal Copper Accents by Lalique Maison']
2025-08-01 03:28:47,946 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '19. Using Patterned Fabrics and Textures to Enhance Visual Comfort': ['Fendi Casa Luxury Damask Patterned Velvet Upholstery Fabric in 100% Silk with Gold Embroidery', 'Ralph Lauren Home Custom-Designed Bold Striped Wool and Cashmere Blend Drapery Panels']
2025-08-01 03:28:47,947 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '20. Creating Visual Depth with Layered Curtains, Rugs, and Textiles': ['The Rug Company "Marrakech" Hand-Knotted Wool and Silk Area Rug in Rich Earth Tones', 'FLOS "IC Lights" Large Pendant Lamp in Chrome with Hand-Blown Murano Glass Shades']
2025-08-01 03:28:47,947 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '21. Practical Tips for Maintaining a Warm and Cozy Atmosphere Year-Round': ['Frette Hotel-Style Lightweight Linen Duvet Cover Set in Italian Flax Linen', 'Restoration Hardware Venetian Linen Sheer Curtains in 100% Italian Linen, Custom-Designed and Made to Measure']
2025-08-01 03:28:47,947 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for 'Conclusion': ['Fendi Casa Velvet Upholstered Sectional Sofa with Gold-Plated Detailing', 'Poltrona Frau Master Leather Recliner with Hand-stitched Italian Leather and Solid Wood Frame']
2025-08-01 03:28:47,954 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 22 product sections into content
2025-08-01 03:28:47,954 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 178 in 43.11s. Added 22 shortcodes for 22 sections.
2025-08-01 03:28:47,955 - amazon_affiliate_integration.processors.content_processor - INFO - Batch processing completed: 2 successful, 0 failed, 0 skipped
2025-08-01 03:28:47,955 - amazon_affiliate_integration - INFO - Updating WordPress content for decorupbeat.com...
2025-08-01 03:28:47,955 - amazon_affiliate_integration.clients.wordpress_client - INFO - Starting batch update of 2 articles on decorupbeat.com
2025-08-01 03:28:50,289 - httpx - INFO - HTTP Request: POST https://decorupbeat.com/wp-json/wp/v2/posts/179 "HTTP/1.1 200 OK"
2025-08-01 03:28:50,575 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 179 on decorupbeat.com
2025-08-01 03:28:51,118 - httpx - INFO - HTTP Request: POST https://decorupbeat.com/wp-json/wp/v2/posts/178 "HTTP/1.1 200 OK"
2025-08-01 03:28:51,401 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 178 on decorupbeat.com
2025-08-01 03:28:51,401 - amazon_affiliate_integration.clients.wordpress_client - INFO - Batch update completed on decorupbeat.com: 2 successful, 0 failed
2025-08-01 03:28:51,401 - amazon_affiliate_integration - INFO - WordPress updates completed: 2 successful, 0 failed
2025-08-01 03:28:51,404 - amazon_affiliate_integration - INFO - Updated state with 2 newly processed URLs
2025-08-01 03:28:51,677 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Post-processing checkpoint for decorupbeat.com (2 articles processed)
2025-08-01 03:28:51,677 - amazon_affiliate_integration - INFO - Domain processing completed for decorupbeat.com: 2 processed, 0 failed, 0 skipped in 67.69s
2025-08-01 03:28:51,678 - amazon_affiliate_integration - INFO - ✅ Phase 1 completed successfully
2025-08-01 03:28:51,678 - amazon_affiliate_integration - INFO - 🕷️ Phase 2 & 3: Crawling and cleaning up failed shortcodes...
2025-08-01 03:28:51,678 - amazon_affiliate_integration - INFO - Starting crawl and cleanup workflow...
2025-08-01 03:28:51,678 - amazon_affiliate_integration - INFO - Phase 1: Crawling articles for visible shortcodes...
2025-08-01 03:28:51,679 - amazon_affiliate_integration - INFO - Using browser crawler for decorupbeat.com
2025-08-01 03:28:52,318 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser initialized with 4 concurrent pages
2025-08-01 03:28:52,319 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Starting browser crawl of 2 articles...
2025-08-01 03:28:57,652 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Progress: 2/2 articles crawled
2025-08-01 03:28:57,652 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser crawl completed: 2/2 successful in 5.33s (0.4 articles/sec)
2025-08-01 03:28:57,764 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser resources cleaned up
2025-08-01 03:28:57,765 - amazon_affiliate_integration - INFO - Crawled 2 articles for domain decorupbeat.com, found 0 visible shortcodes
2025-08-01 03:28:57,765 - amazon_affiliate_integration - INFO - Phase 2: Cleaning up failed shortcodes...
2025-08-01 03:28:57,765 - amazon_affiliate_integration - INFO - Crawl and cleanup workflow completed successfully in 6.09s
2025-08-01 03:28:57,765 - amazon_affiliate_integration - INFO - ✅ Phase 2 & 3 completed successfully
2025-08-01 03:28:57,765 - amazon_affiliate_integration - INFO - 🎉 COMPLETE workflow finished successfully in 73.77s
2025-08-01 03:28:57,766 - __main__ - INFO - Complete workflow finished successfully
