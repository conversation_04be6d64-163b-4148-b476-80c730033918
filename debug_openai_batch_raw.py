#!/usr/bin/env python3
"""
Debug script to see the raw OpenAI batch response
"""

import asyncio
import httpx
from amazon_affiliate_integration.core.config import OPENAI_API_KEY, OPENAI_MODEL

async def test_raw_batch_response():
    """Test raw OpenAI batch response to see the actual format"""
    
    # Test sections
    sections = [
        {
            'heading': '1. Plush Upholstery and Overstuffed Sofas for Instant Comfort',
            'second_paragraph': 'When it comes to creating a warm and cozy living room, nothing beats the comfort of plush upholstery and overstuffed sofas. These pieces serve as the foundation of your seating area, providing both visual appeal and physical comfort.'
        },
        {
            'heading': '2. Layered Textiles: Using Throws, Cushions, and Rugs to Create Warmth',
            'second_paragraph': 'Layering textiles is one of the most effective ways to add warmth and coziness to your living room. Start with a foundation of soft area rugs, then add throw pillows in various textures and patterns.'
        }
    ]
    
    # Create batch prompt (same as in the actual code)
    batch_prompt = """You are a product extraction specialist. For each section below, identify the 2 most expensive purchasable products for the specific application described.

Return your response in this exact format for each section:
SECTION: [section_number]
Product-1: [exact product name]
Product-2: [exact product name]

Sections to analyze:
"""

    # Add each section to the batch prompt
    for i, section in enumerate(sections, 1):
        batch_prompt += f"\nSECTION {i}:\nHeading: {section['heading']}\nContent: {section['second_paragraph']}\n"

    batch_prompt += "\nRemember: Return exactly 2 products per section in the specified format."
    
    print("🔍 Testing raw OpenAI batch response...")
    print("=" * 80)
    print("📝 Prompt being sent:")
    print("-" * 40)
    print(batch_prompt)
    print("-" * 40)
    
    try:
        headers = {
            "Authorization": f"Bearer {OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": OPENAI_MODEL,
            "messages": [
                {"role": "system", "content": "You are a product extraction specialist focused on identifying expensive, purchasable products."},
                {"role": "user", "content": batch_prompt}
            ],
            "max_tokens": 4000,
            "temperature": 0.7
        }

        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            result = response.json()

        content = result['choices'][0]['message']['content'].strip()
        
        print("\n✅ Raw OpenAI Response:")
        print("=" * 80)
        print(content)
        print("=" * 80)
        
        # Test the parsing logic
        print("\n🔍 Testing parsing logic...")
        results = {}
        current_section = None
        current_products = []

        for line in content.split('\n'):
            line = line.strip()
            print(f"Processing line: '{line}'")
            
            if line.startswith('SECTION:'):
                # Save previous section if exists
                if current_section is not None and len(current_products) >= 2:
                    results[sections[current_section - 1]['heading']] = current_products[:2]
                    print(f"  ✅ Saved section {current_section}: {current_products[:2]}")

                # Start new section
                try:
                    current_section = int(line.split(':')[1].strip())
                    current_products = []
                    print(f"  📝 Starting section {current_section}")
                except (ValueError, IndexError) as e:
                    print(f"  ❌ Failed to parse section: {e}")
                    continue

            elif line.startswith('Product-'):
                try:
                    product_name = line.split(':', 1)[1].strip()
                    if product_name:
                        current_products.append(product_name)
                        print(f"  ✅ Found product: {product_name}")
                except IndexError as e:
                    print(f"  ❌ Failed to parse product: {e}")
                    continue

        # Save last section
        if current_section is not None and len(current_products) >= 2:
            results[sections[current_section - 1]['heading']] = current_products[:2]
            print(f"  ✅ Saved final section {current_section}: {current_products[:2]}")
        
        print(f"\n📋 Final parsed results:")
        for heading, products in results.items():
            print(f"  📝 {heading}")
            print(f"     Products: {products}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_raw_batch_response())
