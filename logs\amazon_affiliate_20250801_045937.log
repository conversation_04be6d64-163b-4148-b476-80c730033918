2025-08-01 04:59:37,174 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250801_045937.log
2025-08-01 04:59:37,177 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250801_045937.log
2025-08-01 04:59:37,177 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-01 04:59:37,177 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-01 04:59:37,177 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-01 04:59:37,178 - __main__ - INFO - Starting processing for domain: decorupbeat.com
2025-08-01 04:59:37,179 - amazon_affiliate_integration - INFO - Starting domain processing: decorupbeat.com (force=True, dry_run=False, limit=1)
2025-08-01 04:59:37,179 - amazon_affiliate_integration - INFO - Force mode enabled: ignoring previously processed URLs
2025-08-01 04:59:37,188 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-01 04:59:37,188 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for decorupbeat.com (https://decorupbeat.com)
2025-08-01 04:59:37,578 - httpcore.connection - DEBUG - connect_tcp.started host='decorupbeat.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:59:37,594 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C7A22AB0>
2025-08-01 04:59:37,594 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C9248A50> server_hostname='decorupbeat.com' timeout=30.0
2025-08-01 04:59:37,609 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C7A47C80>
2025-08-01 04:59:37,609 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-01 04:59:37,610 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:59:37,610 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-01 04:59:37,611 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:59:37,611 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 04:59:38,720 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:59:40 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'76'), (b'X-Wp-Totalpages', b'76'), (b'Link', b'<https://decorupbeat.com/wp-json/wp/v2/posts?per_page=1&page=2>; rel="next"'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=dxvT00OQDMK%2FeUgD%2BRbwaV%2FG85NVpCcC7A2v8QqUfkIvqohHy4TwgQTzdauvbQeBrxkGGFt629f09G%2FHny1hWemYGGmpspCQfJLgJyH%2B2w%3D%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'9680b51d99922a54-DAC')])
2025-08-01 04:59:38,721 - httpx - INFO - HTTP Request: GET https://decorupbeat.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-01 04:59:38,721 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-01 04:59:38,723 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:59:38,723 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:59:38,723 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:59:38,723 - httpcore.connection - DEBUG - close.started
2025-08-01 04:59:38,724 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:59:38,724 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for decorupbeat.com
2025-08-01 04:59:38,724 - amazon_affiliate_integration - INFO - Fetching articles from decorupbeat.com...
2025-08-01 04:59:38,724 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from decorupbeat.com since 2025-01-01T00:00:00
2025-08-01 04:59:38,978 - httpcore.connection - DEBUG - connect_tcp.started host='decorupbeat.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:59:38,986 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92D8E60>
2025-08-01 04:59:38,986 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C924BCD0> server_hostname='decorupbeat.com' timeout=30.0
2025-08-01 04:59:39,002 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C9069880>
2025-08-01 04:59:39,002 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-01 04:59:39,002 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:59:39,003 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-01 04:59:39,003 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:59:39,003 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 04:59:40,901 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:59:43 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://decorupbeat.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'76'), (b'X-Wp-Totalpages', b'1'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=mfyk%2Boo0FcXeAY%2B9sETFOIO25ALED7ZoWfJsXTEpTIcjwI%2B3ahdXUG7iNGDCBGXn3spOEZtEbG1JfUAPKV5tV43oojIEcSPNXZ8Y6MSBBg%3D%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'9680b5264b03ba4d-DAC')])
2025-08-01 04:59:40,902 - httpx - INFO - HTTP Request: GET https://decorupbeat.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-01 04:59:40,902 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-01 04:59:42,122 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:59:42,123 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:59:42,123 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:59:42,125 - httpcore.connection - DEBUG - close.started
2025-08-01 04:59:42,126 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:59:42,141 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 76 articles from page 1
2025-08-01 04:59:42,142 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 1 articles for testing
2025-08-01 04:59:42,142 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from decorupbeat.com: 1
2025-08-01 04:59:42,143 - amazon_affiliate_integration.clients.wordpress_client - DEBUG - Converted 1 articles to ArticleInfo objects
2025-08-01 04:59:42,144 - amazon_affiliate_integration - INFO - Converted 1 articles to ArticleInfo objects
2025-08-01 04:59:42,144 - amazon_affiliate_integration - INFO - Force mode: processing 1 articles (excluding 0 excluded)
2025-08-01 04:59:42,389 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Pre-processing checkpoint for decorupbeat.com (1 articles)
2025-08-01 04:59:42,389 - amazon_affiliate_integration - INFO - Created git checkpoint before processing decorupbeat.com
2025-08-01 04:59:42,389 - amazon_affiliate_integration - INFO - Processing 1 articles for decorupbeat.com...
2025-08-01 04:59:42,389 - amazon_affiliate_integration.processors.content_processor - INFO - Starting batch processing of 1 articles with concurrency limit 4
2025-08-01 04:59:42,389 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 179: 21 Embracing Warm and Cozy Living Room Designs for Comfort
2025-08-01 04:59:42,392 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: decorupbeat.com_179_pre_processing_20250801_045942.json
2025-08-01 04:59:42,394 - amazon_affiliate_integration.processors.content_processor - DEBUG - Parsed 22 H2 sections from content
2025-08-01 04:59:42,394 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 22 sections using batch processing
2025-08-01 04:59:42,395 - amazon_affiliate_integration.processors.content_processor - INFO - Using individual processing for reliable product generation
2025-08-01 04:59:42,686 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:59:42,755 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C90C83E0>
2025-08-01 04:59:42,755 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C924A8D0> server_hostname='api.openai.com' timeout=30.0
2025-08-01 04:59:42,768 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C886B9E0>
2025-08-01 04:59:42,769 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 04:59:42,769 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:59:42,769 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 04:59:42,770 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:59:42,770 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 04:59:45,654 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:59:47 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'572'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'621'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999667'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'4ms'), (b'x-request-id', b'req_c5db8e85fa9c41513cde626db171a32e'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=qUVmbVFQiA8iEvZmf0aCh_PMhgYpq7.dMxceq8BIulY-1754002787-*******-xnJUxvH.eMRhVeUKGDT.DHbcuOcwSFdQKXuTj7VxKYO47M76XAr6PszuQXVgTNdsSYwjRa5BlwcAbVJqP6BxtlCf9PMA139jFoi.mpmk_YI; path=/; expires=Thu, 31-Jul-25 23:29:47 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=nn_5Km4E_ynj6XQQmHAu1SGG3ra12GBq6iZ6hG7hhUk-1754002787814-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b53dd8dd5942-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 04:59:45,656 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 04:59:45,656 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 04:59:45,762 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:59:45,763 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:59:45,763 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:59:45,763 - httpcore.connection - DEBUG - close.started
2025-08-01 04:59:45,764 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:59:45,764 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '1. Plush Upholstery and Overstuffed Sofas for Instant Comfort': ['Fendi Casa Vintage Velvet Oversized Sofa in Luxurious Beige Leather and Velvet Mix', 'Roche Boulangerie Custom Made Overstuffed Chesterfield Sofa in Fine Italian Silk Velvet']
2025-08-01 04:59:45,764 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Plush Upholstery and Overstuffed Sofas for Instant Comfort': ['Fendi Casa Vintage Velvet Oversized Sofa in Luxurious Beige Leather and Velvet Mix', 'Roche Boulangerie Custom Made Overstuffed Chesterfield Sofa in Fine Italian Silk Velvet']
2025-08-01 04:59:46,137 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:59:46,152 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92DA000>
2025-08-01 04:59:46,152 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92C1F50> server_hostname='api.openai.com' timeout=30.0
2025-08-01 04:59:46,167 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92DAFF0>
2025-08-01 04:59:46,168 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 04:59:46,168 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:59:46,169 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 04:59:46,169 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:59:46,169 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 04:59:49,353 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:59:51 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'2039'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'2063'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999661'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'5ms'), (b'x-request-id', b'req_619d34b7641716adabc44401cd855f9a'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=WqwWt94GM_24ITXx1CWkb6IJOTOLEc7L6M0Q.JucLFE-1754002791-*******-Jx9j2EraKanwC7W.KrOTinLCBCThFHNrplZ8f15QOLCQH.6IlzXl4JLBuShYX5rMwETBW88a5TV2MIrIe0g4budanF9YrJUppHeW5sNf_.I; path=/; expires=Thu, 31-Jul-25 23:29:51 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=7irxOGJovk0Idm6v1St1J9kPiQzqYrpyrc9ic9lDXPM-1754002791513-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b5531fc8ba53-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 04:59:49,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 04:59:49,354 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 04:59:49,358 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:59:49,358 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:59:49,360 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:59:49,360 - httpcore.connection - DEBUG - close.started
2025-08-01 04:59:49,361 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:59:49,361 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '2. Layered Textiles: Using Throws, Cushions, and Rugs to Create Warmth': ['Stark Carpet Wool Area Rug in 100% Hand-Spun Shetland Wool, Custom Designed for Luxury Interiors', 'Fendi Casa Luxe Faux Fur Throw in Deep Burgundy with Cashmere Blend and Embellished Edging']
2025-08-01 04:59:49,361 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Layered Textiles: Using Throws, Cushions, and Rugs to Create Warmth': ['Stark Carpet Wool Area Rug in 100% Hand-Spun Shetland Wool, Custom Designed for Luxury Interiors', 'Fendi Casa Luxe Faux Fur Throw in Deep Burgundy with Cashmere Blend and Embellished Edging']
2025-08-01 04:59:49,715 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:59:49,723 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92D8C50>
2025-08-01 04:59:49,724 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92C3F50> server_hostname='api.openai.com' timeout=30.0
2025-08-01 04:59:49,739 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92D9280>
2025-08-01 04:59:49,740 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 04:59:49,740 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:59:49,740 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 04:59:49,741 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:59:49,741 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 04:59:51,308 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:59:53 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'754'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'864'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999671'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'4ms'), (b'x-request-id', b'req_4296bed93cc1e952903de168eaf46668'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=p1vE2JyBE8CZGq4cPjSjPeSD3XGhJdo4f4eDb4N9JnQ-1754002793-*******-bpNf_yGpW7JoUFxRo8yECa.AsbRtkmHZ3JDXzuJkVql6x.DuKhlDfFOg8o7aYVh4rECsGgvpjWB6rokEMFHSvcPmmVlpJHNPdl9AYFXiJJ0; path=/; expires=Thu, 31-Jul-25 23:29:53 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=fYI8.gY1u13VkYRz7.uVxVqUv0AhynDyql3JWQD.klY-1754002793468-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b56969dbba5c-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 04:59:51,308 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 04:59:51,309 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 04:59:51,309 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:59:51,309 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:59:51,310 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:59:51,310 - httpcore.connection - DEBUG - close.started
2025-08-01 04:59:51,310 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:59:51,310 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '3. Warm Color Palettes: Incorporating Earth Tones and Muted Shades': ['Farrow & Ball Estate Emulsion Paint in Dusty Rose (Luxury Premium Wall Paint with Handcrafted Pigments)', 'Roche Boussel Leather Upholstered Armchair in Slate Gray Leather with Gold Leaf Detailing']
2025-08-01 04:59:51,311 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Warm Color Palettes: Incorporating Earth Tones and Muted Shades': ['Farrow & Ball Estate Emulsion Paint in Dusty Rose (Luxury Premium Wall Paint with Handcrafted Pigments)', 'Roche Boussel Leather Upholstered Armchair in Slate Gray Leather with Gold Leaf Detailing']
2025-08-01 04:59:51,631 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:59:51,642 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92DBF20>
2025-08-01 04:59:51,642 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92C2550> server_hostname='api.openai.com' timeout=30.0
2025-08-01 04:59:51,655 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92DBCE0>
2025-08-01 04:59:51,655 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 04:59:51,656 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:59:51,656 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 04:59:51,656 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:59:51,656 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 04:59:53,483 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:59:55 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'837'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'891'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999663'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'5ms'), (b'x-request-id', b'req_0cf2f982b912d9b3cc7b22f9139a42d9'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=l8Gv9dnkZst342Wv9P9fOQDe71ZtsIRiu_hUkhif4VY-1754002795-*******-WMOIomWwjF25o29KTkOvmFUHvouQbUfb9As8u5ugvzwcGQLDtrXqOExWmios5.ztdM.KnLk2cgTVHT66LtU4_Aja_N5pHKrNZKGc_LQ6X5Y; path=/; expires=Thu, 31-Jul-25 23:29:55 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=YNdTsoTj83UGUsmVPY23Q001dByY3ikOk360lssEjoc-1754002795644-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b5756b0f71f4-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 04:59:53,484 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 04:59:53,485 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 04:59:53,485 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:59:53,485 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:59:53,486 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:59:53,486 - httpcore.connection - DEBUG - close.started
2025-08-01 04:59:53,486 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:59:53,486 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '4. Ambient Lighting: Soft Lamps and Warm LED Fixtures for Relaxing Atmosphere': ['Flos IC T Table Lamp by Michael Anastassiades – Handmade Murano Glass with Brass Details, Premium Finish', 'LUMIBRIGHT Customizable LED Strip Lighting System with Wi-Fi Control and High-Color Rendering Index (CRI) in Aluminum Housing']
2025-08-01 04:59:53,487 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Ambient Lighting: Soft Lamps and Warm LED Fixtures for Relaxing Atmosphere': ['Flos IC T Table Lamp by Michael Anastassiades – Handmade Murano Glass with Brass Details, Premium Finish', 'LUMIBRIGHT Customizable LED Strip Lighting System with Wi-Fi Control and High-Color Rendering Index (CRI) in Aluminum Housing']
2025-08-01 04:59:53,817 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:59:53,828 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C930D520>
2025-08-01 04:59:53,828 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92C35D0> server_hostname='api.openai.com' timeout=30.0
2025-08-01 04:59:53,995 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C930D160>
2025-08-01 04:59:53,995 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 04:59:53,995 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:59:53,996 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 04:59:53,996 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:59:53,996 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 04:59:55,350 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:59:57 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'555'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'566'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999649'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'5ms'), (b'x-request-id', b'req_8531003cca1f4c43ec886c8d422d1b72'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=EjZwyuT8QM5hvr8Wsd0CsACP6vqC8VjWqyPYlBrK2KE-1754002797-*******-tQuhHthCWvDy79o.obyhlkFpYGNCxIZJinZp2BguF0XV6nltUmDQfexiYePhEuHlE8zwB9GFbogACj3eKx.ydCQBKduxXX1fXc9TNpdbLqo; path=/; expires=Thu, 31-Jul-25 23:29:57 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=XG_1Nmu3edddkZEi9JLzbzHr0n.hlRjZOAgYy_hY6Ag-1754002797510-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b5840adeba5c-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 04:59:55,351 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 04:59:55,351 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 04:59:55,351 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:59:55,352 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:59:55,352 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:59:55,352 - httpcore.connection - DEBUG - close.started
2025-08-01 04:59:55,352 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:59:55,353 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '5. Natural Materials: Incorporating Wood, Wool, and Leather Accents': ['BDDW Reclaimed Oak Coffee Table with Hand-Finished Matte Surface', 'Fendi Casa Luxurious Leather Accent Armchair in Cognac Calfskin']
2025-08-01 04:59:55,353 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Natural Materials: Incorporating Wood, Wool, and Leather Accents': ['BDDW Reclaimed Oak Coffee Table with Hand-Finished Matte Surface', 'Fendi Casa Luxurious Leather Accent Armchair in Cognac Calfskin']
2025-08-01 04:59:55,683 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:59:55,691 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C930EAB0>
2025-08-01 04:59:55,692 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92C38D0> server_hostname='api.openai.com' timeout=30.0
2025-08-01 04:59:55,701 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C930E7B0>
2025-08-01 04:59:55,702 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 04:59:55,702 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:59:55,702 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 04:59:55,703 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:59:55,703 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 04:59:57,373 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:59:59 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'548'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'560'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999674'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'4ms'), (b'x-request-id', b'req_6bc97e779cc7f0213fb7e9f6e8d1e2ba'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=mto6e9c14ClGR7mR4oj4232_HZR6j0RoUAyMRf8Bxvo-1754002799-*******-.zJJAiXQKiRUCFAjwbGPPCJsgegkRh6CQ2Gr1UgN57yyUUYJbiaGRRb2YJUqRs4ZSJzEOw3Aqd2He59asrOx8SKpRBhJntf2lSg_4WBA4.U; path=/; expires=Thu, 31-Jul-25 23:29:59 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=J4D035FbLEMAljErU3tJzFt1enUxfQmmxZG.EBRq8sY-1754002799532-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b58eaa3171f4-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 04:59:57,374 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 04:59:57,374 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 04:59:57,375 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:59:57,376 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:59:57,376 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:59:57,376 - httpcore.connection - DEBUG - close.started
2025-08-01 04:59:57,377 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:59:57,377 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '6. Fireplace Focal Points: Designing with Central Fireplaces or Faux Alternatives': ['Restoration Hardware Antique Limestone Gas Fireplace Insert with Custom Mantel', 'Fendi Casa Luxury Faux Fireplace with Hand-Carved Marble Surround']
2025-08-01 04:59:57,377 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Fireplace Focal Points: Designing with Central Fireplaces or Faux Alternatives': ['Restoration Hardware Antique Limestone Gas Fireplace Insert with Custom Mantel', 'Fendi Casa Luxury Faux Fireplace with Hand-Carved Marble Surround']
2025-08-01 04:59:57,702 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:59:57,713 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931C110>
2025-08-01 04:59:57,713 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92C3DD0> server_hostname='api.openai.com' timeout=30.0
2025-08-01 04:59:57,725 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C930F0B0>
2025-08-01 04:59:57,726 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 04:59:57,727 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:59:57,727 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 04:59:57,727 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:59:57,727 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:00:27,736 - httpcore.http11 - DEBUG - receive_response_headers.failed exception=ReadTimeout(TimeoutError())
2025-08-01 05:00:27,736 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:00:27,737 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:00:27,737 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 1 failed: . Retrying in 1.0s...
2025-08-01 05:00:28,981 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:00:39,971 - httpcore.connection - DEBUG - connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-01 05:00:40,003 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 2 failed: All connection attempts failed. Retrying in 2.0s...
2025-08-01 05:00:42,323 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:00:42,324 - httpcore.connection - DEBUG - connect_tcp.failed exception=ConnectError(gaierror(11001, 'getaddrinfo failed'))
2025-08-01 05:00:42,324 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 3 failed: [Errno 11001] getaddrinfo failed. Retrying in 4.0s...
2025-08-01 05:00:46,543 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:00:46,544 - httpcore.connection - DEBUG - connect_tcp.failed exception=ConnectError(gaierror(11001, 'getaddrinfo failed'))
2025-08-01 05:00:46,545 - amazon_affiliate_integration.clients.openai_client - ERROR - All 4 attempts failed. Last error: [Errno 11001] getaddrinfo failed
2025-08-01 05:00:46,545 - amazon_affiliate_integration.clients.openai_client - ERROR - OpenAI API error for heading '7. Cozy Nook Corners: Creating Inviting Spaces for Reading or Relaxing': [Errno 11001] getaddrinfo failed
2025-08-01 05:00:46,545 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Cozy Nook Corners: Creating Inviting Spaces for Reading or Relaxing': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 05:00:47,070 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:00:47,071 - httpcore.connection - DEBUG - connect_tcp.failed exception=ConnectError(gaierror(11001, 'getaddrinfo failed'))
2025-08-01 05:00:47,072 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 1 failed: [Errno 11001] getaddrinfo failed. Retrying in 1.0s...
2025-08-01 05:00:48,323 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:00:48,324 - httpcore.connection - DEBUG - connect_tcp.failed exception=ConnectError(gaierror(11001, 'getaddrinfo failed'))
2025-08-01 05:00:48,324 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 2 failed: [Errno 11001] getaddrinfo failed. Retrying in 2.0s...
2025-08-01 05:00:50,589 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:00:50,590 - httpcore.connection - DEBUG - connect_tcp.failed exception=ConnectError(gaierror(11001, 'getaddrinfo failed'))
2025-08-01 05:00:50,590 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 3 failed: [Errno 11001] getaddrinfo failed. Retrying in 4.0s...
2025-08-01 05:00:54,843 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:00:54,843 - httpcore.connection - DEBUG - connect_tcp.failed exception=ConnectError(gaierror(11001, 'getaddrinfo failed'))
2025-08-01 05:00:54,844 - amazon_affiliate_integration.clients.openai_client - ERROR - All 4 attempts failed. Last error: [Errno 11001] getaddrinfo failed
2025-08-01 05:00:54,844 - amazon_affiliate_integration.clients.openai_client - ERROR - OpenAI API error for heading '8. Soft Area Rugs to Add Texture and Comfort Underfoot': [Errno 11001] getaddrinfo failed
2025-08-01 05:00:54,844 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Soft Area Rugs to Add Texture and Comfort Underfoot': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 05:00:55,402 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:00:55,403 - httpcore.connection - DEBUG - connect_tcp.failed exception=ConnectError(gaierror(11001, 'getaddrinfo failed'))
2025-08-01 05:00:55,404 - amazon_affiliate_integration.clients.openai_client - WARNING - Attempt 1 failed: [Errno 11001] getaddrinfo failed. Retrying in 1.0s...
2025-08-01 05:00:56,713 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:00:56,850 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92DB7D0>
2025-08-01 05:00:56,855 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92F7950> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:00:56,877 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92DB2F0>
2025-08-01 05:00:56,879 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:00:56,880 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:00:56,882 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:00:56,882 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:00:56,883 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:00:58,399 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:00 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'1223'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'1235'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999666'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'5ms'), (b'x-request-id', b'req_c37ffa1999c129d97b40ca4dd6d492e4'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=2RDOVo9ffNLOZHMb9OOC1tCQEaBHjnbE7l8ODc.EtAM-1754002860-*******-sRETe7tYIEVbZB.j4neXQCMtL5Cca_vVFQRwbH7L.hu8K.rQoZrVLbRTXPYEHi3EPZaL..4mJ5J2Tb4RhE.j2jgjjVV2bMSZKPXrQ0qa3t4; path=/; expires=Thu, 31-Jul-25 23:31:00 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=BcYDfOpCGa6k6YaVETLRdVumlQ8iqUynr21ppAFOwdU-1754002860560-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b70d0ca52a54-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:00:58,399 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:00:58,400 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:00:58,400 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:00:58,400 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:00:58,401 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:00:58,401 - httpcore.connection - DEBUG - close.started
2025-08-01 05:00:58,401 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:00:58,402 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '9. Intimate Seating Arrangements for Family and Guest Comfort': ['Roche Brouhard Velvet Lounge Sofa by Roche Brouhard — Customizable in luxurious velvet upholstery with solid hardwood frame and premium cushioning for ultimate comfort and elegance.', 'Fendi Casa Decorium Modular Sectional Sofa — High-end, designer velvet-upholstered sectional with handcrafted details, gold-plated accents, and bespoke dimensions for intimate family and guest seating.']
2025-08-01 05:00:58,402 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Intimate Seating Arrangements for Family and Guest Comfort': ['Roche Brouhard Velvet Lounge Sofa by Roche Brouhard — Customizable in luxurious velvet upholstery with solid hardwood frame and premium cushioning for ultimate comfort and elegance.', 'Fendi Casa Decorium Modular Sectional Sofa — High-end, designer velvet-upholstered sectional with handcrafted details, gold-plated accents, and bespoke dimensions for intimate family and guest seating.']
2025-08-01 05:00:58,813 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:00:58,819 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92D90D0>
2025-08-01 05:00:58,819 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92F76D0> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:00:58,832 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92D9F10>
2025-08-01 05:00:58,833 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:00:58,834 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:00:58,834 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:00:58,834 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:00:58,835 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:00:59,872 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:02 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'753'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'781'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999668'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'4ms'), (b'x-request-id', b'req_3cb6bcb9ce8acb9464ceae2da22ca478'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=4sfR3lyPnIL4bLSQLLe1HxvSPqzArXkQlTbPbkEBPYE-1754002862-*******-LIazWZ5NaJruKUR9aAVT7I5vtEboV1f_f4nxTyKBqD.OoXy5E0zytqDqN8TaTNZU5liLqgbpiol2qy4tPD7vHkg7hgATCPzlDNYvkM1YiJQ; path=/; expires=Thu, 31-Jul-25 23:31:02 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=_3WbNVD6a.DW1i5gLh5QVbi_3_5xpu7xBYU7OKIB5gU-1754002862033-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b7194ba42a54-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:00:59,873 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:00:59,873 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:00:59,874 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:00:59,874 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:00:59,874 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:00:59,875 - httpcore.connection - DEBUG - close.started
2025-08-01 05:00:59,875 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:00:59,877 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '10. Warm Metallic Accents: Brass, Gold, or Copper Details for a Cozy Glow': ['Roche Boussel Brass and Marble Side Table by Roche Boussel', 'Waterford Crystal Lismore Diamond Gold-Plated Candle Holders']
2025-08-01 05:00:59,878 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Warm Metallic Accents: Brass, Gold, or Copper Details for a Cozy Glow': ['Roche Boussel Brass and Marble Side Table by Roche Boussel', 'Waterford Crystal Lismore Diamond Gold-Plated Candle Holders']
2025-08-01 05:01:00,407 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:00,415 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931CC80>
2025-08-01 05:01:00,415 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92C3350> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:01:00,429 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931CA40>
2025-08-01 05:01:00,430 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:00,431 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:00,431 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:00,431 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:00,431 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:01,722 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:03 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'508'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'520'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999662'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'5ms'), (b'x-request-id', b'req_37d9ec72ff0712a67003f4e8f7d9f8b2'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=mClbsbX0P3Mg5H9g_xCeMv3tuLCSvcgnOWXWKaOChTw-1754002863-*******-CjeijLs.mt_TMZDh5S64LI6IiGKa7NCOaroRCX44ogfuemEAvgMEjp0oFewTXCYz0CX51xKvoOJX1Et044jcZd1q.kLcKMMrwNfeIvvLxhA; path=/; expires=Thu, 31-Jul-25 23:31:03 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=7Bp8hURIqU0oVn7pg1SAlxXQdM.7CfxR1cyAQuKGrOc-1754002863883-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b7233bb9ba4a-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:01:01,723 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:01:01,723 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:01,724 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:01,724 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:01,724 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:01,725 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:01,725 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:01,725 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '11. Decorative Candle Arrangements for a Soft, Warm Ambiance': ['Diptyque Baies Scented Candle – 190g in Hand-Blown Glass Vessel', 'Cire Trudon Abd El Kader Large Taper Candle in Gold-Plated Holder']
2025-08-01 05:01:01,725 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Decorative Candle Arrangements for a Soft, Warm Ambiance': ['Diptyque Baies Scented Candle – 190g in Hand-Blown Glass Vessel', 'Cire Trudon Abd El Kader Large Taper Candle in Gold-Plated Holder']
2025-08-01 05:01:02,161 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:02,457 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C930F3E0>
2025-08-01 05:01:02,457 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92F7CD0> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:01:02,474 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C930F2F0>
2025-08-01 05:01:02,474 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:02,475 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:02,475 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:02,475 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:02,475 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:03,795 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:05 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'617'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'628'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999662'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'5ms'), (b'x-request-id', b'req_9d715e1da0e3ed37347cb201f6f33dd6'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=JReKv5.z60XO3zrv7BoUKfsJTWTFTnBwbVz9tnw2kZw-1754002865-*******-Ca_hzllKV2E68yg30zoEjLoX61a3RnwV2iEfu5UGqkQ6BH3g0ePqgjidf6dk3GLtlyRkE0e4jMxh5AzeSCxpYHy0TKlwLAah57DElJe8VMI; path=/; expires=Thu, 31-Jul-25 23:31:05 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=Khg5cHXD6s7C.zp.F.9MRIXsgMz2AxlW2JYdSLoAHXo-1754002865956-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b7300d2ad058-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:01:03,796 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:01:03,796 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:03,797 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:03,797 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:03,798 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:03,798 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:03,799 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:03,799 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '12. Layered Window Treatments: Using Heavy Curtains and Sheers for a Snug Feel': ['FLOS IC Lights Pendant Lamp in Handblown Murano Glass with Gold-Plated Details', 'Restoration Hardware Luxe Velvet Curtain Panels in Deep Burgundy with Custom Silver Grommets']
2025-08-01 05:01:03,799 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Layered Window Treatments: Using Heavy Curtains and Sheers for a Snug Feel': ['FLOS IC Lights Pendant Lamp in Handblown Murano Glass with Gold-Plated Details', 'Restoration Hardware Luxe Velvet Curtain Panels in Deep Burgundy with Custom Silver Grommets']
2025-08-01 05:01:04,444 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:04,455 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92D9B20>
2025-08-01 05:01:04,457 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92F77D0> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:01:04,475 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92D9400>
2025-08-01 05:01:04,478 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:04,492 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:04,494 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:04,494 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:04,494 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:05,725 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:07 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'947'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'962'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999668'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'4ms'), (b'x-request-id', b'req_384d13771e21dd9887b1a134d3727c02'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=yDBf3Ry9giT13Oxh35IdvZGEg9.xSSjKx.7evNjMeyE-1754002867-*******-.O97O1gCQtN2Zll5CtAYpM65OX7VTbEd1l4f4HBez0rn6pNP0tXD2yx1WO1QCk.6tgnHGkuPkeiClQG39H1NHmWTg8f1TMgZQ95afq8vQD0; path=/; expires=Thu, 31-Jul-25 23:31:07 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=OGVvVJAoDYwdUfL_iNgmuT1IdV2qD9xDD8MCRzDn.OY-1754002867886-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b73cadb5ded3-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:01:05,725 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:01:05,726 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:05,730 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:05,730 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:05,730 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:05,731 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:05,731 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:05,731 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '13. Textured Wall Coverings: Using Woven or Fabric Wall Panels for Added Warmth': ['The Wall & Coverings™ Designer Woven Wall Panel in Handwoven Silk and Gold Leaf Accents', 'Loro Piana Interiors Luxury Wool and Linen Textured Wall Panels in Custom Geometric Layouts']
2025-08-01 05:01:05,732 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '13. Textured Wall Coverings: Using Woven or Fabric Wall Panels for Added Warmth': ['The Wall & Coverings™ Designer Woven Wall Panel in Handwoven Silk and Gold Leaf Accents', 'Loro Piana Interiors Luxury Wool and Linen Textured Wall Panels in Custom Geometric Layouts']
2025-08-01 05:01:06,320 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:06,328 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92D94F0>
2025-08-01 05:01:06,329 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92F7850> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:01:06,340 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92DA960>
2025-08-01 05:01:06,341 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:06,342 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:06,342 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:06,342 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:06,342 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:07,172 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:09 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'546'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'560'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999659'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'5ms'), (b'x-request-id', b'req_9af11d4e798ff8017a63a416cd549d2e'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=yKhzampDSYentUxLSfkfTy7DsIHPSwxkS2cpoNRMAwY-1754002869-*******-FyI7KYmZ_a6hPRtrSX1agCZML3jKlY6NUZ7on4p5daAkgbB_.vIa_exl_XI.8I6aLYZHlDAlnIRgnvP16mVq54qgur9uqeVv6W17CsGlpfY; path=/; expires=Thu, 31-Jul-25 23:31:09 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=Qeic1A1dgJlZ.FDlV43c9sPVJ5xJCjQIKrWo.cMdEY4-1754002869333-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b7483e162a54-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:01:07,173 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:01:07,174 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:07,175 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:07,175 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:07,175 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:07,175 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:07,176 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:07,176 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '14. Multi-Functional Furniture: Ottoman Storage and Reclining Options for Practical Comfort': ['Restoration Hardware Cloud Velvet Ottoman with Storage and Reclining Functionality', 'Roche Bosc Plush Velvet Storage Ottoman with Motorized Recline and Solid Brass Accents']
2025-08-01 05:01:07,176 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '14. Multi-Functional Furniture: Ottoman Storage and Reclining Options for Practical Comfort': ['Restoration Hardware Cloud Velvet Ottoman with Storage and Reclining Functionality', 'Roche Bosc Plush Velvet Storage Ottoman with Motorized Recline and Solid Brass Accents']
2025-08-01 05:01:07,623 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:07,632 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C92DB260>
2025-08-01 05:01:07,632 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92F7350> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:01:07,646 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C8E9BF20>
2025-08-01 05:01:07,647 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:07,647 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:07,647 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:07,648 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:07,650 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:08,545 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:10 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'599'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'614'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999669'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'4ms'), (b'x-request-id', b'req_ec9c78979c5d9aff4339fc9976ae91d0'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=Hrr6eTYoF60gBFDzlocf8xM6ALRbwJS4GYIRASth8gE-1754002870-*******-rfPh_rdMjLmp6mxWaGqRaA2VLm5S6TiXENc3TsoCGbY0cAeq5B319tqPvgnp2Wi19Ye3fBWIR9F.ILH_w8mP9M6lTUbrsJKtka35WV5Vs0M; path=/; expires=Thu, 31-Jul-25 23:31:10 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=5xojWiD._83QRrsO3mu93Y1xgGAdUzOk8hCZhbPjjpY-1754002870706-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b7505de4ba5b-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:01:08,545 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:01:08,546 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:08,547 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:08,547 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:08,547 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:08,547 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:08,548 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:08,548 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '15. Vintage and Rustic Decor Elements to Add Character and Warmth': ['Restoration Hardware Vintage Reclaimed Wood Side Table with Brass Detailing', 'Jonathan Adler Large Antique Brass Lanterns with Hand-Patinated Finish']
2025-08-01 05:01:08,548 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '15. Vintage and Rustic Decor Elements to Add Character and Warmth': ['Restoration Hardware Vintage Reclaimed Wood Side Table with Brass Detailing', 'Jonathan Adler Large Antique Brass Lanterns with Hand-Patinated Finish']
2025-08-01 05:01:08,994 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:09,008 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931CBC0>
2025-08-01 05:01:09,008 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C924BCD0> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:01:09,025 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931D160>
2025-08-01 05:01:09,025 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:09,026 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:09,027 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:09,027 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:09,028 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:10,467 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:12 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'660'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'674'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999652'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'5ms'), (b'x-request-id', b'req_c81d129dd796b16c59420cd59fd283c0'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=nWv_UphF5b_Yn64e9g5Y6_54TImuGGj3X9Yi9uetui4-1754002872-*******-Cjk6K.XdiO6xPwssK_EFe0.XEkI6noHkjXwnwUgQW3HSaTzYOi_TWT7GPZ0_xskr4yofzcnwxWpssE.dC8pR6kHv0UgNykX0bgzkXWtdfSI; path=/; expires=Thu, 31-Jul-25 23:31:12 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=rfl.wSEySnQj5f15FN9llktwdsRVJCtOf4AlhziFLLM-1754002872629-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b758fd1cba5b-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:01:10,469 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:01:10,469 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:10,474 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:10,474 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:10,474 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:10,474 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:10,475 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:10,475 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '16. Use of Cozy Throw Blankets and Quilts for Instant Snugness': ['Frette Luxe Wool Throw Blanket in Cashmere-Blend with Signature Embroidered Edging', 'Restoration Hardware Belgian Linen Quilted Bedspread with Hand-Embroidered Vintage Floral Details']
2025-08-01 05:01:10,475 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '16. Use of Cozy Throw Blankets and Quilts for Instant Snugness': ['Frette Luxe Wool Throw Blanket in Cashmere-Blend with Signature Embroidered Edging', 'Restoration Hardware Belgian Linen Quilted Bedspread with Hand-Embroidered Vintage Floral Details']
2025-08-01 05:01:10,862 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:10,871 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931F860>
2025-08-01 05:01:10,873 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C924A8D0> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:01:10,887 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931F740>
2025-08-01 05:01:10,887 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:10,888 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:10,888 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:10,889 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:10,889 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:12,241 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:14 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'527'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'540'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999672'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'4ms'), (b'x-request-id', b'req_011387a8efcdd730450fc00028ca313b'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=op3HGeq2COgm1v4mV9DYqwdAMhoUZU3PoT6rUKA3MQM-1754002874-*******-0dr_YQ4qbs0cqd_l52S.yQfOl9O4M8bC7XEjU0.RxkUw5FMmh4He0iDg.w1RJfoXbtn9KDJPLrLQRh0PJyK304KluTDEWZwLsVteisySnvk; path=/; expires=Thu, 31-Jul-25 23:31:14 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=zVWWz82xqVla427cxy5BYPS0nTFVTHrY9jWo3M8vrdQ-1754002874398-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b7649ea1814e-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:01:12,245 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:01:12,249 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:12,250 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:12,250 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:12,250 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:12,251 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:12,251 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:12,251 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '17. Incorporating Soft, Diffused Light with Fairy Lights or String Lights': ['Philips Hue Amarant Pendant String Lights – Customizable RGBW Smart Fairy Lights with Premium Fabric Coating and App Control', 'Luminae Luxe Crystal Fairy String Lights – Handcrafted Swarovski Crystal-Embedded LED Fairy Lights with Gold-Plated Wiring']
2025-08-01 05:01:12,252 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '17. Incorporating Soft, Diffused Light with Fairy Lights or String Lights': ['Philips Hue Amarant Pendant String Lights – Customizable RGBW Smart Fairy Lights with Premium Fabric Coating and App Control', 'Luminae Luxe Crystal Fairy String Lights – Handcrafted Swarovski Crystal-Embedded LED Fairy Lights with Gold-Plated Wiring']
2025-08-01 05:01:12,620 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:12,632 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C932CEC0>
2025-08-01 05:01:12,633 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92C3050> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:01:12,645 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C932C710>
2025-08-01 05:01:12,645 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:12,646 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:12,646 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:12,646 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:12,647 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:13,989 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:16 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'621'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'633'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999645'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'5ms'), (b'x-request-id', b'req_c51318f7244eac94a7d6fb18537f5f4b'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=I6J14tle9G0N8ZsSLR.xTiJ64NNNk.9we_afwjNKwNo-1754002876-*******-kfQc0MQXLGPrRMJ0KvcyakDmEEmwQD2BIleqDySMQzrvjVAgOwhDzrb_1skBofKAJkwKhh.SiqwxS3wMXVwQtJ5zlYQ.LulllhFPlsQPHh0; path=/; expires=Thu, 31-Jul-25 23:31:16 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=YoC3N3SEP_8ASA1vSbb8VO0tFvdjs4tVGAZ2hUGCQIw-1754002876151-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b76f982ff257-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:01:13,991 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:01:13,991 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:13,992 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:13,993 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:13,994 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:13,995 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:13,996 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:13,997 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '18. Adding Small Decorative Sculptures and Ceramic Accents for Visual Warmth': ['Jonathan Adler Large Glazed Ceramic Vase in Earthy Tones', 'Lalique Ailleurs Metallic Brass and Glass Sculpture Collection']
2025-08-01 05:01:13,998 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '18. Adding Small Decorative Sculptures and Ceramic Accents for Visual Warmth': ['Jonathan Adler Large Glazed Ceramic Vase in Earthy Tones', 'Lalique Ailleurs Metallic Brass and Glass Sculpture Collection']
2025-08-01 05:01:14,465 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:14,473 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C932E480>
2025-08-01 05:01:14,474 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92F5DD0> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:01:14,489 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C932E0C0>
2025-08-01 05:01:14,490 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:14,491 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:14,492 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:14,492 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:14,492 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:17,070 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:19 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'1751'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'1778'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999672'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'4ms'), (b'x-request-id', b'req_5a7d5ab93fd7de362b3a8e0699783169'), (b'x-envoy-decorator-operation', b'router-canary.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=mmdOWpRPWvhFR8IUkPXENTrKARvo23U62bZo5_FpN2c-1754002879-*******-SSEn.MrgJRRNE1pSav.YypPdSjnUE2F1_njsHOT9zSJJMae8SUkl3uvJvy6Jx.qq8wNtgefKgktivG9gcW_H4.vP.su82fxMXOUQrJaDDeQ; path=/; expires=Thu, 31-Jul-25 23:31:19 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=l2jHYsIgzFqEMy8pkMf4LmOve8VuowiTBQP89kbwINE-1754002879229-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b77b2966ba4f-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:01:17,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:01:17,096 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:17,138 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:17,157 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:17,168 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:17,170 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:17,170 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:17,182 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '19. Using Patterned Fabrics and Textures to Enhance Visual Comfort': ['Frette Maison Damask Velvet Upholstery Fabric in 100% Silk Blend, Luxury Designer Patterned Textiles', 'Osborne & Little "Moorish" Embroidered Silk Tapestry Wallpaper with Metallic Accents']
2025-08-01 05:01:17,198 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '19. Using Patterned Fabrics and Textures to Enhance Visual Comfort': ['Frette Maison Damask Velvet Upholstery Fabric in 100% Silk Blend, Luxury Designer Patterned Textiles', 'Osborne & Little "Moorish" Embroidered Silk Tapestry Wallpaper with Metallic Accents']
2025-08-01 05:01:17,785 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:17,800 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931FFE0>
2025-08-01 05:01:17,800 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C9248A50> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:01:17,814 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931FB00>
2025-08-01 05:01:17,814 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:17,815 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:17,815 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:17,816 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:17,816 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:19,418 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:21 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'502'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'788'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999668'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'4ms'), (b'x-request-id', b'req_2a194e1622e3fe2197c407219e822ed7'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=DziGhmMnXOjkHn27eqWvgMGXxriQUr4HkNJc_8JKj6g-1754002881-*******-YszgEQkmmHvWahGoIfacMfdvUV5dgRW5wvB6d3GnvxNY5K3EFRvRIpYBplydlc_Gr3huiIaBRvmAhG0ebal7932QOrFhzlfYnts8vGjhJLU; path=/; expires=Thu, 31-Jul-25 23:31:21 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=HuFN.fpxZtkJE9LDPl21ae7FM1f8SkK1uLzKKZGTbH4-1754002881573-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b78fee46814e-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:01:19,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:01:19,422 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:19,423 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:19,423 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:19,423 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:19,424 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:19,424 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:19,424 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '20. Creating Visual Depth with Layered Curtains, Rugs, and Textiles': ['The Rug Company Hand-Knotted Tibetan Wool and Silk Area Rug in Custom Dimensions', 'Frette Luxe Velvet Layered Curtains with Gold Embellishments']
2025-08-01 05:01:19,426 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '20. Creating Visual Depth with Layered Curtains, Rugs, and Textiles': ['The Rug Company Hand-Knotted Tibetan Wool and Silk Area Rug in Custom Dimensions', 'Frette Luxe Velvet Layered Curtains with Gold Embellishments']
2025-08-01 05:01:19,896 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:19,904 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931DAC0>
2025-08-01 05:01:19,904 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C924BCD0> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:01:19,918 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931C7A0>
2025-08-01 05:01:19,919 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:19,921 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:19,922 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:19,922 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:19,922 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:27,013 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:29 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'2103'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'6277'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999660'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'5ms'), (b'x-request-id', b'req_06c1ea09ebed9bb86b594066855996e9'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=UW1Po44AOsgA8Hw8DLRGWFW6aOeTWaAp8XhizzrUfr0-1754002889-*******-oCnU3brTUbn8s6nxzdoZ8E0d6FEb1FcfczmjIXEp5AXvv83BconZ95dAOotTTlEhr6qGKOb_.WCx4y75TliSrI_8JdXrAcemgQqeYIuLGkI; path=/; expires=Thu, 31-Jul-25 23:31:29 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=tQTExWJp0ytusSqnZZTkWfHXHI2x.TtkHm5lexpXszY-1754002889174-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b79d1d7571f4-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:01:27,019 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:01:27,021 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:27,021 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:27,022 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:27,022 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:27,022 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:27,022 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:27,024 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '21. Practical Tips for Maintaining a Warm and Cozy Atmosphere Year-Round': ['Frette Hotel Line Egyptian Cotton Duvet Cover and Pillowcase Set', 'Restoration Hardware Luxe Linen Sheer Curtain Panels in Premium Silk Blend']
2025-08-01 05:01:27,024 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '21. Practical Tips for Maintaining a Warm and Cozy Atmosphere Year-Round': ['Frette Hotel Line Egyptian Cotton Duvet Cover and Pillowcase Set', 'Restoration Hardware Luxe Linen Sheer Curtain Panels in Premium Silk Blend']
2025-08-01 05:01:27,487 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:27,498 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931D340>
2025-08-01 05:01:27,498 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C92F6F50> server_hostname='api.openai.com' timeout=30.0
2025-08-01 05:01:27,513 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C931E360>
2025-08-01 05:01:27,514 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:27,515 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:27,515 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:27,521 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:27,524 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:29,146 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:31 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'585'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'605'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999690'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'4ms'), (b'x-request-id', b'req_6213fa0f3dde3440b2cde5d7746e9135'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=7YC_ahvY64y4bTwtCIq8xL2TInhqMNOngx2lbxUPHgw-1754002891-*******-81K.koyVei2oTP_2WMO7DtZE7RcISHLklnPBzl0dMqvO6yLF2I5XZBH7A0XPvHugLClHxoOqC28o6X5h_Y93jr2s_dzSOz81dmybNy71E8w; path=/; expires=Thu, 31-Jul-25 23:31:31 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=yS0nKyumhiuNVv0K7v0DnyLHvEnz6nXKKHe1mzmNCow-1754002891308-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680b7cc88cbba56-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 05:01:29,147 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 05:01:29,147 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:29,148 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:29,149 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:29,149 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:29,149 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:29,149 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:29,151 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for 'Conclusion': ['Restoration Hardware Cloud Modular Sectional Sofa in Luxe Velvet with Solid Wood Frame', 'Fendi Casa Crystal-Embellished Gold Leaf Floor Lamp']
2025-08-01 05:01:29,151 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for 'Conclusion': ['Restoration Hardware Cloud Modular Sectional Sofa in Luxe Velvet with Solid Wood Frame', 'Fendi Casa Crystal-Embellished Gold Leaf Floor Lamp']
2025-08-01 05:01:29,260 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Fendi Casa Vintage Velvet Oversized Sofa in Luxurious Beige Leather and Velvet Mix' after heading '1. Plush Upholstery and Overstuffed Sofas for Instant Comfort'
2025-08-01 05:01:29,261 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Stark Carpet Wool Area Rug in 100% Hand-Spun Shetland Wool, Custom Designed for Luxury Interiors' after heading '2. Layered Textiles: Using Throws, Cushions, and Rugs to Create Warmth'
2025-08-01 05:01:29,262 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Farrow & Ball Estate Emulsion Paint in Dusty Rose (Luxury Premium Wall Paint with Handcrafted Pigments)' after heading '3. Warm Color Palettes: Incorporating Earth Tones and Muted Shades'
2025-08-01 05:01:29,262 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Flos IC T Table Lamp by Michael Anastassiades – Handmade Murano Glass with Brass Details, Premium Finish' after heading '4. Ambient Lighting: Soft Lamps and Warm LED Fixtures for Relaxing Atmosphere'
2025-08-01 05:01:29,263 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'BDDW Reclaimed Oak Coffee Table with Hand-Finished Matte Surface' after heading '5. Natural Materials: Incorporating Wood, Wool, and Leather Accents'
2025-08-01 05:01:29,263 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Restoration Hardware Antique Limestone Gas Fireplace Insert with Custom Mantel' after heading '6. Fireplace Focal Points: Designing with Central Fireplaces or Faux Alternatives'
2025-08-01 05:01:29,264 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '7. Cozy Nook Corners: Creating Inviting Spaces for Reading or Relaxing'
2025-08-01 05:01:29,264 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '8. Soft Area Rugs to Add Texture and Comfort Underfoot'
2025-08-01 05:01:29,264 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Roche Brouhard Velvet Lounge Sofa by Roche Brouhard — Customizable in luxurious velvet upholstery with solid hardwood frame and premium cushioning for ultimate comfort and elegance.' after heading '9. Intimate Seating Arrangements for Family and Guest Comfort'
2025-08-01 05:01:29,265 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Roche Boussel Brass and Marble Side Table by Roche Boussel' after heading '10. Warm Metallic Accents: Brass, Gold, or Copper Details for a Cozy Glow'
2025-08-01 05:01:29,265 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Diptyque Baies Scented Candle – 190g in Hand-Blown Glass Vessel' after heading '11. Decorative Candle Arrangements for a Soft, Warm Ambiance'
2025-08-01 05:01:29,266 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'FLOS IC Lights Pendant Lamp in Handblown Murano Glass with Gold-Plated Details' after heading '12. Layered Window Treatments: Using Heavy Curtains and Sheers for a Snug Feel'
2025-08-01 05:01:29,266 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'The Wall & Coverings™ Designer Woven Wall Panel in Handwoven Silk and Gold Leaf Accents' after heading '13. Textured Wall Coverings: Using Woven or Fabric Wall Panels for Added Warmth'
2025-08-01 05:01:29,267 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Restoration Hardware Cloud Velvet Ottoman with Storage and Reclining Functionality' after heading '14. Multi-Functional Furniture: Ottoman Storage and Reclining Options for Practical Comfort'
2025-08-01 05:01:29,268 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Restoration Hardware Vintage Reclaimed Wood Side Table with Brass Detailing' after heading '15. Vintage and Rustic Decor Elements to Add Character and Warmth'
2025-08-01 05:01:29,269 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Frette Luxe Wool Throw Blanket in Cashmere-Blend with Signature Embroidered Edging' after heading '16. Use of Cozy Throw Blankets and Quilts for Instant Snugness'
2025-08-01 05:01:29,269 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Philips Hue Amarant Pendant String Lights – Customizable RGBW Smart Fairy Lights with Premium Fabric Coating and App Control' after heading '17. Incorporating Soft, Diffused Light with Fairy Lights or String Lights'
2025-08-01 05:01:29,270 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Jonathan Adler Large Glazed Ceramic Vase in Earthy Tones' after heading '18. Adding Small Decorative Sculptures and Ceramic Accents for Visual Warmth'
2025-08-01 05:01:29,270 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Frette Maison Damask Velvet Upholstery Fabric in 100% Silk Blend, Luxury Designer Patterned Textiles' after heading '19. Using Patterned Fabrics and Textures to Enhance Visual Comfort'
2025-08-01 05:01:29,271 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'The Rug Company Hand-Knotted Tibetan Wool and Silk Area Rug in Custom Dimensions' after heading '20. Creating Visual Depth with Layered Curtains, Rugs, and Textiles'
2025-08-01 05:01:29,271 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Frette Hotel Line Egyptian Cotton Duvet Cover and Pillowcase Set' after heading '21. Practical Tips for Maintaining a Warm and Cozy Atmosphere Year-Round'
2025-08-01 05:01:29,272 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Restoration Hardware Cloud Modular Sectional Sofa in Luxe Velvet with Solid Wood Frame' after heading 'Conclusion'
2025-08-01 05:01:29,272 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 22 product sections into content
2025-08-01 05:01:29,272 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 179 in 106.88s. Added 22 shortcodes for 22 sections.
2025-08-01 05:01:29,273 - amazon_affiliate_integration.processors.content_processor - INFO - Batch processing completed: 1 successful, 0 failed, 0 skipped
2025-08-01 05:01:29,274 - amazon_affiliate_integration - INFO - Updating WordPress content for decorupbeat.com...
2025-08-01 05:01:29,274 - amazon_affiliate_integration.clients.wordpress_client - INFO - Starting batch update of 1 articles on decorupbeat.com
2025-08-01 05:01:29,671 - httpcore.connection - DEBUG - connect_tcp.started host='decorupbeat.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 05:01:29,691 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C930E300>
2025-08-01 05:01:29,691 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5C9058F50> server_hostname='decorupbeat.com' timeout=30.0
2025-08-01 05:01:29,707 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5C930F1A0>
2025-08-01 05:01:29,708 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 05:01:29,710 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 05:01:29,711 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 05:01:29,711 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 05:01:29,712 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 05:01:30,724 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 23:01:32 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://decorupbeat.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'Allow', b'GET, POST, PUT, PATCH, DELETE'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=KG4mjCbuhCT%2FQjmCVxkcMAd%2B9DSfz1PVe%2BPeI4dT7p8ytxjf7pXMz6OM%2By%2FTnozs94lFEBLeW7Yh7XsOs8NoZaayaaLgG%2FOLVkprpskyzg%3D%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'9680b7da3d2d33bc-DAC')])
2025-08-01 05:01:30,727 - httpx - INFO - HTTP Request: POST https://decorupbeat.com/wp-json/wp/v2/posts/179 "HTTP/1.1 200 OK"
2025-08-01 05:01:30,734 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 05:01:30,740 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 05:01:30,746 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 05:01:30,753 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 05:01:30,754 - httpcore.connection - DEBUG - close.started
2025-08-01 05:01:30,756 - httpcore.connection - DEBUG - close.complete
2025-08-01 05:01:30,757 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 179 on decorupbeat.com
2025-08-01 05:01:30,757 - amazon_affiliate_integration.clients.wordpress_client - INFO - Batch update completed on decorupbeat.com: 1 successful, 0 failed
2025-08-01 05:01:30,757 - amazon_affiliate_integration - INFO - WordPress updates completed: 1 successful, 0 failed
2025-08-01 05:01:30,759 - amazon_affiliate_integration.processors.state_manager - DEBUG - Created backup: D:\Amazon_Addon_For_Pinterest_Automation_v2\processed_urls.bak.20250801_050130
2025-08-01 05:01:30,769 - amazon_affiliate_integration.processors.state_manager - DEBUG - Successfully wrote D:\Amazon_Addon_For_Pinterest_Automation_v2\processed_urls.json
2025-08-01 05:01:30,774 - amazon_affiliate_integration - INFO - Updated state with 1 newly processed URLs
2025-08-01 05:01:35,987 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Post-processing checkpoint for decorupbeat.com (1 articles processed)
2025-08-01 05:01:35,988 - amazon_affiliate_integration - INFO - Domain processing completed for decorupbeat.com: 1 processed, 0 failed, 0 skipped in 118.81s
