# Phase 3: Crawler & Quality Assurance - User Guide

## Overview

Phase 3 introduces advanced crawler and quality assurance capabilities to the Amazon Affiliate Integration system. This phase automatically detects failed shortcodes on live websites and cleans them up to maintain content quality.

## New Features

### 🕷️ Shortcode Crawler
- **Purpose**: Crawls live WordPress articles to detect visible (failed) shortcodes
- **Technology**: Async HTTP requests with configurable concurrency
- **Detection**: Uses regex pattern matching to find visible Amazon shortcodes
- **Performance**: Processes 4 articles concurrently by default (configurable)

### 🧹 Cleanup Processor  
- **Purpose**: Automatically removes failed shortcodes from WordPress articles
- **Safety**: Creates backups before making changes
- **Intelligence**: Preserves Gutenberg block structure during cleanup
- **State Tracking**: Updates missing products tracking after successful cleanup

### 📊 Missing Products Tracking
- **Purpose**: Tracks which articles have failed shortcodes
- **Storage**: JSON-based state management (`missing_products.json`)
- **Structure**: Organized by URL → heading → failed products
- **Integration**: Seamlessly integrates with existing state management

## Command Line Interface

### New Commands

#### Complete Workflow (Process → Crawl → Cleanup)
```bash
# Complete end-to-end workflow: Process articles → Crawl → Cleanup failed shortcodes
python enhanced_amazon_affiliate_integration.py --full-workflow --domain example.com

# Complete workflow for all domains with force processing
python enhanced_amazon_affiliate_integration.py --full-workflow --all-domains --force

# Complete workflow with dry run (no actual changes)
python enhanced_amazon_affiliate_integration.py --full-workflow --domain example.com --dry-run
```

#### Crawl and Cleanup Only
```bash
# Run crawl and cleanup workflow (assumes articles already processed)
python enhanced_amazon_affiliate_integration.py --crawl-and-cleanup --domain example.com

# Run crawl and cleanup for all domains
python enhanced_amazon_affiliate_integration.py --crawl-and-cleanup --all-domains
```

#### Crawl Only
```bash
# Only crawl for visible shortcodes (no cleanup)
python enhanced_amazon_affiliate_integration.py --crawl-only --domain example.com

# Crawl all domains
python enhanced_amazon_affiliate_integration.py --crawl-only --all-domains
```

#### Cleanup Only
```bash
# Only cleanup based on existing missing products data
python enhanced_amazon_affiliate_integration.py --cleanup-only --domain example.com

# Cleanup all domains
python enhanced_amazon_affiliate_integration.py --cleanup-only --all-domains
```

#### Processing (Updated)
```bash
# Process articles (original functionality)
python enhanced_amazon_affiliate_integration.py --process --domain example.com
python enhanced_amazon_affiliate_integration.py --process --all-domains --force
```

## Workflow Phases

### Complete Workflow (--full-workflow)
The complete workflow executes all three phases automatically:

1. **Processing Phase**: Adds Amazon shortcodes to articles (Phase 1 & 2 functionality)
2. **Crawling Phase**: Detects visible (failed) shortcodes on live sites
3. **Cleanup Phase**: Removes failed shortcodes to maintain content quality

This is the **recommended approach** for most users as it provides end-to-end automation.

### Individual Workflow Phases

#### Phase 1: Crawling (--crawl-only or --crawl-and-cleanup)
1. **Article Discovery**: Loads processed URLs from state management
2. **HTTP Requests**: Makes async requests to live article URLs
3. **Content Analysis**: Parses HTML to detect visible shortcodes
4. **Pattern Matching**: Uses regex to extract failed product names
5. **State Updates**: Records missing products in `missing_products.json`

### Phase 2: Cleanup
1. **Target Identification**: Identifies articles with visible shortcodes
2. **Backup Creation**: Creates safety backups before modifications
3. **Content Processing**: Removes failed shortcode blocks
4. **WordPress Updates**: Updates article content via REST API
5. **State Cleanup**: Removes cleaned URLs from missing products tracking

## Configuration

### Crawler Settings
Located in `amazon_affiliate_integration/core/config.py`:

```python
CRAWLER_CONFIG = {
    'concurrency_limit': 4,      # Max concurrent HTTP requests
    'request_delay': 0.5,        # Delay between requests (seconds)
    'timeout': 30,               # Request timeout (seconds)
    'max_retries': 3,            # Max retry attempts per request
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) ...'
}
```

### Customization
- **Concurrency**: Adjust `concurrency_limit` based on server capacity
- **Rate Limiting**: Modify `request_delay` to respect website policies
- **Timeouts**: Increase `timeout` for slow-loading sites
- **Retries**: Adjust `max_retries` for unreliable connections

## State Files

### missing_products.json
Tracks articles with failed shortcodes:

```json
{
  "articles": {
    "https://example.com/article-1": {
      "headings": {
        "Recommended Products to replicate this idea": {
          "missing_products": ["wireless headphones", "bluetooth speaker"],
          "detected_at": "2025-01-31T10:30:00"
        }
      },
      "total_missing": 2,
      "last_updated": "2025-01-31T10:30:00"
    }
  },
  "last_updated": "2025-01-31T10:30:00",
  "total_articles_with_issues": 1
}
```

## Error Handling

### Network Issues
- **Automatic Retries**: Up to 3 attempts with exponential backoff
- **Timeout Handling**: Graceful handling of slow responses
- **Connection Errors**: Comprehensive error logging and recovery

### WordPress API Issues
- **Authentication**: Validates credentials before operations
- **Rate Limiting**: Respects WordPress API rate limits
- **Backup Safety**: Always creates backups before modifications

### State Management
- **Atomic Operations**: Prevents data corruption during updates
- **Recovery**: Automatic recovery from partial failures
- **Validation**: Validates state file integrity

## Monitoring & Logging

### Progress Reporting
- Real-time progress updates during crawling and cleanup
- Statistics on shortcodes found and removed
- Performance metrics (response times, success rates)

### Logging Levels
- **INFO**: General progress and success messages
- **WARNING**: Non-critical issues (timeouts, retries)
- **ERROR**: Critical failures requiring attention
- **DEBUG**: Detailed technical information

## Best Practices

### Before Running
1. **Backup**: Ensure git repository is clean and committed
2. **Validation**: Run `--validate` to check system health
3. **Testing**: Use `--crawl-only` first to assess scope

### During Operation
1. **Monitor Logs**: Watch for errors or warnings
2. **Check Progress**: Monitor statistics and completion rates
3. **Resource Usage**: Ensure server can handle concurrent requests

### After Completion
1. **Review Results**: Check cleanup statistics
2. **Verify Changes**: Spot-check cleaned articles
3. **State Validation**: Ensure state files are updated correctly

## Troubleshooting

### Common Issues

#### High Failure Rates
- **Cause**: Network issues, server overload, or rate limiting
- **Solution**: Reduce concurrency limit, increase delays

#### Missing Shortcodes Not Detected
- **Cause**: Shortcodes may be working intermittently
- **Solution**: Run crawler multiple times, check regex patterns

#### Cleanup Failures
- **Cause**: WordPress API issues or permission problems
- **Solution**: Validate credentials, check API connectivity

#### State File Corruption
- **Cause**: Interrupted operations or disk issues
- **Solution**: Restore from backup, clear state if necessary

### Support Commands

```bash
# Validate system health
python enhanced_amazon_affiliate_integration.py --validate

# Clear all state (use with caution)
# This would need to be implemented as a separate utility

# View current state statistics
# Check the state files directly or implement a status command
```

## Integration with Existing Workflow

Phase 3 seamlessly integrates with the existing processing workflow:

1. **Phase 1 & 2**: Process articles and add shortcodes (existing functionality)
2. **Phase 3**: Crawl processed articles and cleanup failed shortcodes (new)
3. **Ongoing**: Regular cleanup runs to maintain content quality

### Recommended Schedule
- **Processing**: Weekly or bi-weekly for new content
- **Crawling**: Daily to detect new failures quickly  
- **Cleanup**: Immediately after crawling to maintain quality

## Performance Expectations

### Crawling Performance
- **Speed**: ~4 articles per second (with default settings)
- **Accuracy**: >95% detection rate for visible shortcodes
- **Resource Usage**: Low CPU, moderate network bandwidth

### Cleanup Performance  
- **Speed**: ~2-3 articles per second (WordPress API dependent)
- **Safety**: 100% backup creation before modifications
- **Success Rate**: >98% for accessible articles

## Future Enhancements

Phase 3 provides the foundation for additional quality assurance features:
- **Scheduled Monitoring**: Automated daily/weekly crawling
- **Performance Analytics**: Detailed shortcode success/failure metrics
- **Smart Retry**: Intelligent retry logic for intermittent failures
- **Notification System**: Alerts for high failure rates

---

**Note**: Phase 3 requires all Phase 1 and Phase 2 components to be properly configured and functional. Ensure your system passes validation before using Phase 3 features.
