2025-08-01 04:54:14,693 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250801_045414.log
2025-08-01 04:54:14,694 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250801_045414.log
2025-08-01 04:54:14,694 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-01 04:54:14,694 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-01 04:54:14,694 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-01 04:54:14,704 - __main__ - INFO - Starting processing for domain: decorupbeat.com
2025-08-01 04:54:14,704 - amazon_affiliate_integration - INFO - Starting domain processing: decorupbeat.com (force=False, dry_run=False, limit=1)
2025-08-01 04:54:14,705 - amazon_affiliate_integration - INFO - Loaded 0 previously processed URLs
2025-08-01 04:54:14,713 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-01 04:54:14,713 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for decorupbeat.com (https://decorupbeat.com)
2025-08-01 04:54:15,013 - httpcore.connection - DEBUG - connect_tcp.started host='decorupbeat.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:54:15,027 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001AA9EDAC1D0>
2025-08-01 04:54:15,028 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001AA9EF7CD50> server_hostname='decorupbeat.com' timeout=30.0
2025-08-01 04:54:15,042 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001AA9EE39910>
2025-08-01 04:54:15,042 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-01 04:54:15,043 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:54:15,043 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-01 04:54:15,043 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:54:15,044 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 04:54:16,157 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:54:18 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'76'), (b'X-Wp-Totalpages', b'76'), (b'Link', b'<https://decorupbeat.com/wp-json/wp/v2/posts?per_page=1&page=2>; rel="next"'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=kjueVjrlXyCUE7cM34N62YOdadRhve4VSjIAZR6xVl6zdP7ak4jQSzUo0zf1ngIZNXTk1XAPB3NaKN37%2BNaHuLbb4LKkRiFj1zhxyq0iGA%3D%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'9680ad3d89d12a54-DAC')])
2025-08-01 04:54:16,158 - httpx - INFO - HTTP Request: GET https://decorupbeat.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-01 04:54:16,159 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-01 04:54:16,160 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:54:16,160 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:54:16,160 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:54:16,161 - httpcore.connection - DEBUG - close.started
2025-08-01 04:54:16,161 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:54:16,161 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for decorupbeat.com
2025-08-01 04:54:16,162 - amazon_affiliate_integration - INFO - Fetching articles from decorupbeat.com...
2025-08-01 04:54:16,162 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from decorupbeat.com since 2025-01-01T00:00:00
2025-08-01 04:54:16,448 - httpcore.connection - DEBUG - connect_tcp.started host='decorupbeat.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:54:16,459 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001AA9F014F80>
2025-08-01 04:54:16,460 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001AA9EFF68D0> server_hostname='decorupbeat.com' timeout=30.0
2025-08-01 04:54:16,473 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001AA9BE72870>
2025-08-01 04:54:16,474 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-01 04:54:16,474 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:54:16,475 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-01 04:54:16,475 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:54:16,475 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 04:54:18,302 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:54:20 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://decorupbeat.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'76'), (b'X-Wp-Totalpages', b'1'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=d6F3Ufv2cuG7FUG5JXOyJxXUSxjfjnHI%2FR2biDaBkz%2FjMa2uqn%2FKId3qO1RqkCn72gD7BY2O7vm6E1XcwgxhMCblTHO74gYjI4RKcTKQ3g%3D%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'9680ad467a01ba5c-DAC')])
2025-08-01 04:54:18,303 - httpx - INFO - HTTP Request: GET https://decorupbeat.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-01 04:54:18,303 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-01 04:54:19,544 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:54:19,544 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:54:19,544 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:54:19,547 - httpcore.connection - DEBUG - close.started
2025-08-01 04:54:19,548 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:54:19,568 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 76 articles from page 1
2025-08-01 04:54:19,569 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 1 articles for testing
2025-08-01 04:54:19,569 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from decorupbeat.com: 1
2025-08-01 04:54:19,571 - amazon_affiliate_integration.clients.wordpress_client - DEBUG - Converted 1 articles to ArticleInfo objects
2025-08-01 04:54:19,571 - amazon_affiliate_integration - INFO - Converted 1 articles to ArticleInfo objects
2025-08-01 04:54:19,571 - amazon_affiliate_integration - INFO - Normal mode: processing 1 articles (skipping 0 processed, 0 excluded)
2025-08-01 04:54:19,911 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Pre-processing checkpoint for decorupbeat.com (1 articles)
2025-08-01 04:54:19,911 - amazon_affiliate_integration - INFO - Created git checkpoint before processing decorupbeat.com
2025-08-01 04:54:19,912 - amazon_affiliate_integration - INFO - Processing 1 articles for decorupbeat.com...
2025-08-01 04:54:19,912 - amazon_affiliate_integration.processors.content_processor - INFO - Starting batch processing of 1 articles with concurrency limit 4
2025-08-01 04:54:19,912 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 179: 21 Embracing Warm and Cozy Living Room Designs for Comfort
2025-08-01 04:54:19,916 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: decorupbeat.com_179_pre_processing_20250801_045419.json
2025-08-01 04:54:19,917 - amazon_affiliate_integration.processors.content_processor - DEBUG - Parsed 22 H2 sections from content
2025-08-01 04:54:19,918 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 22 sections using batch processing
2025-08-01 04:54:19,918 - amazon_affiliate_integration.clients.openai_client - INFO - Analyzing 22 sections in single batch API call
2025-08-01 04:54:20,190 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=60.0 socket_options=None
2025-08-01 04:54:20,345 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001AA9EFE3320>
2025-08-01 04:54:20,345 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001AA9ED9D250> server_hostname='api.openai.com' timeout=60.0
2025-08-01 04:54:20,360 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001AA9EFE3380>
2025-08-01 04:54:20,361 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 04:54:20,362 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:54:20,363 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 04:54:20,363 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:54:20,364 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 04:54:32,984 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:54:35 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'10635'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'10725'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3997814'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'32ms'), (b'x-request-id', b'req_e17f096729e4cd34da705253fb98a582'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=LhCw6ldFinddWdv3asY.kPf_ocRvUPOFdVYG0FqZ3kU-1754002475-1.0.1.1-1Qr7jpzRMVJ.5JICaNo.WszGVL7NaQAXMoorJAc1afFFwVdFl11CYV.iDq8fm.mmdwwrFiFUVwS.HyAPwEaGrz3FScfOm9OY8R5XHneFuIA; path=/; expires=Thu, 31-Jul-25 23:24:35 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=sI4NKkaVseDJI.ZQUjIBBhGRmOrzCN8X_1xKudc_eHQ-1754002475137-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9680ad5ecbbdf257-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-01 04:54:32,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 04:54:32,986 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 04:54:32,986 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:54:32,987 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:54:32,987 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:54:32,987 - httpcore.connection - DEBUG - close.started
2025-08-01 04:54:32,987 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:54:32,988 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '1. Plush Upholstery and Overstuffed Sofas for Instant Comfort', using fallback
2025-08-01 04:54:32,988 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '2. Layered Textiles: Using Throws, Cushions, and Rugs to Create Warmth', using fallback
2025-08-01 04:54:32,988 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '3. Warm Color Palettes: Incorporating Earth Tones and Muted Shades', using fallback
2025-08-01 04:54:32,988 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '4. Ambient Lighting: Soft Lamps and Warm LED Fixtures for Relaxing Atmosphere', using fallback
2025-08-01 04:54:32,988 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '5. Natural Materials: Incorporating Wood, Wool, and Leather Accents', using fallback
2025-08-01 04:54:32,989 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '6. Fireplace Focal Points: Designing with Central Fireplaces or Faux Alternatives', using fallback
2025-08-01 04:54:32,989 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '7. Cozy Nook Corners: Creating Inviting Spaces for Reading or Relaxing', using fallback
2025-08-01 04:54:32,989 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '8. Soft Area Rugs to Add Texture and Comfort Underfoot', using fallback
2025-08-01 04:54:32,989 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '9. Intimate Seating Arrangements for Family and Guest Comfort', using fallback
2025-08-01 04:54:32,989 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '10. Warm Metallic Accents: Brass, Gold, or Copper Details for a Cozy Glow', using fallback
2025-08-01 04:54:32,990 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '11. Decorative Candle Arrangements for a Soft, Warm Ambiance', using fallback
2025-08-01 04:54:32,990 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '12. Layered Window Treatments: Using Heavy Curtains and Sheers for a Snug Feel', using fallback
2025-08-01 04:54:32,990 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '13. Textured Wall Coverings: Using Woven or Fabric Wall Panels for Added Warmth', using fallback
2025-08-01 04:54:32,990 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '14. Multi-Functional Furniture: Ottoman Storage and Reclining Options for Practical Comfort', using fallback
2025-08-01 04:54:32,990 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '15. Vintage and Rustic Decor Elements to Add Character and Warmth', using fallback
2025-08-01 04:54:32,990 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '16. Use of Cozy Throw Blankets and Quilts for Instant Snugness', using fallback
2025-08-01 04:54:32,990 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '17. Incorporating Soft, Diffused Light with Fairy Lights or String Lights', using fallback
2025-08-01 04:54:32,990 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '18. Adding Small Decorative Sculptures and Ceramic Accents for Visual Warmth', using fallback
2025-08-01 04:54:32,990 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '19. Using Patterned Fabrics and Textures to Enhance Visual Comfort', using fallback
2025-08-01 04:54:32,992 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '20. Creating Visual Depth with Layered Curtains, Rugs, and Textiles', using fallback
2025-08-01 04:54:32,992 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section '21. Practical Tips for Maintaining a Warm and Cozy Atmosphere Year-Round', using fallback
2025-08-01 04:54:32,992 - amazon_affiliate_integration.clients.openai_client - WARNING - No products found for section 'Conclusion', using fallback
2025-08-01 04:54:32,992 - amazon_affiliate_integration.clients.openai_client - INFO - Batch analysis completed: 22 sections processed in single API call
2025-08-01 04:54:32,993 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Plush Upholstery and Overstuffed Sofas for Instant Comfort': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,993 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Layered Textiles: Using Throws, Cushions, and Rugs to Create Warmth': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,993 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Warm Color Palettes: Incorporating Earth Tones and Muted Shades': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,993 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Ambient Lighting: Soft Lamps and Warm LED Fixtures for Relaxing Atmosphere': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,993 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Natural Materials: Incorporating Wood, Wool, and Leather Accents': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,994 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Fireplace Focal Points: Designing with Central Fireplaces or Faux Alternatives': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,994 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Cozy Nook Corners: Creating Inviting Spaces for Reading or Relaxing': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,994 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Soft Area Rugs to Add Texture and Comfort Underfoot': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,994 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Intimate Seating Arrangements for Family and Guest Comfort': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,994 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Warm Metallic Accents: Brass, Gold, or Copper Details for a Cozy Glow': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,994 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Decorative Candle Arrangements for a Soft, Warm Ambiance': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,995 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Layered Window Treatments: Using Heavy Curtains and Sheers for a Snug Feel': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,995 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '13. Textured Wall Coverings: Using Woven or Fabric Wall Panels for Added Warmth': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,995 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '14. Multi-Functional Furniture: Ottoman Storage and Reclining Options for Practical Comfort': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,995 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '15. Vintage and Rustic Decor Elements to Add Character and Warmth': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,995 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '16. Use of Cozy Throw Blankets and Quilts for Instant Snugness': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,996 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '17. Incorporating Soft, Diffused Light with Fairy Lights or String Lights': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,996 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '18. Adding Small Decorative Sculptures and Ceramic Accents for Visual Warmth': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,996 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '19. Using Patterned Fabrics and Textures to Enhance Visual Comfort': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,996 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '20. Creating Visual Depth with Layered Curtains, Rugs, and Textiles': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,996 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '21. Practical Tips for Maintaining a Warm and Cozy Atmosphere Year-Round': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,996 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for 'Conclusion': ['Premium Home Decor Item', 'Luxury Interior Accessory']
2025-08-01 04:54:32,997 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '1. Plush Upholstery and Overstuffed Sofas for Instant Comfort'
2025-08-01 04:54:32,997 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '2. Layered Textiles: Using Throws, Cushions, and Rugs to Create Warmth'
2025-08-01 04:54:32,998 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '3. Warm Color Palettes: Incorporating Earth Tones and Muted Shades'
2025-08-01 04:54:32,998 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '4. Ambient Lighting: Soft Lamps and Warm LED Fixtures for Relaxing Atmosphere'
2025-08-01 04:54:32,999 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '5. Natural Materials: Incorporating Wood, Wool, and Leather Accents'
2025-08-01 04:54:32,999 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '6. Fireplace Focal Points: Designing with Central Fireplaces or Faux Alternatives'
2025-08-01 04:54:33,000 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '7. Cozy Nook Corners: Creating Inviting Spaces for Reading or Relaxing'
2025-08-01 04:54:33,000 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '8. Soft Area Rugs to Add Texture and Comfort Underfoot'
2025-08-01 04:54:33,001 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '9. Intimate Seating Arrangements for Family and Guest Comfort'
2025-08-01 04:54:33,001 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '10. Warm Metallic Accents: Brass, Gold, or Copper Details for a Cozy Glow'
2025-08-01 04:54:33,002 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '11. Decorative Candle Arrangements for a Soft, Warm Ambiance'
2025-08-01 04:54:33,002 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '12. Layered Window Treatments: Using Heavy Curtains and Sheers for a Snug Feel'
2025-08-01 04:54:33,003 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '13. Textured Wall Coverings: Using Woven or Fabric Wall Panels for Added Warmth'
2025-08-01 04:54:33,003 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '14. Multi-Functional Furniture: Ottoman Storage and Reclining Options for Practical Comfort'
2025-08-01 04:54:33,004 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '15. Vintage and Rustic Decor Elements to Add Character and Warmth'
2025-08-01 04:54:33,004 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '16. Use of Cozy Throw Blankets and Quilts for Instant Snugness'
2025-08-01 04:54:33,005 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '17. Incorporating Soft, Diffused Light with Fairy Lights or String Lights'
2025-08-01 04:54:33,005 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '18. Adding Small Decorative Sculptures and Ceramic Accents for Visual Warmth'
2025-08-01 04:54:33,006 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '19. Using Patterned Fabrics and Textures to Enhance Visual Comfort'
2025-08-01 04:54:33,006 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '20. Creating Visual Depth with Layered Curtains, Rugs, and Textiles'
2025-08-01 04:54:33,006 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading '21. Practical Tips for Maintaining a Warm and Cozy Atmosphere Year-Round'
2025-08-01 04:54:33,008 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section for 'Premium Home Decor Item' after heading 'Conclusion'
2025-08-01 04:54:33,009 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 22 product sections into content
2025-08-01 04:54:33,009 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 179 in 13.10s. Added 22 shortcodes for 22 sections.
2025-08-01 04:54:33,010 - amazon_affiliate_integration.processors.content_processor - INFO - Batch processing completed: 1 successful, 0 failed, 0 skipped
2025-08-01 04:54:33,010 - amazon_affiliate_integration - INFO - Updating WordPress content for decorupbeat.com...
2025-08-01 04:54:33,010 - amazon_affiliate_integration.clients.wordpress_client - INFO - Starting batch update of 1 articles on decorupbeat.com
2025-08-01 04:54:33,252 - httpcore.connection - DEBUG - connect_tcp.started host='decorupbeat.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 04:54:33,262 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001AA9F015CD0>
2025-08-01 04:54:33,262 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001AA9EF7EBD0> server_hostname='decorupbeat.com' timeout=30.0
2025-08-01 04:54:33,277 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001AA9E5AB9E0>
2025-08-01 04:54:33,277 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-01 04:54:33,277 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-01 04:54:33,277 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-01 04:54:33,278 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-01 04:54:33,278 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 04:54:34,804 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 31 Jul 2025 22:54:36 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://decorupbeat.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'Allow', b'GET, POST, PUT, PATCH, DELETE'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=paQXFuWUYf9DFjCSKYNFlUCP6n47nsaQsT%2BPht8YrRfPAciPLJ1MYIR87UJEvP5FAROjH5sGF5LiLoknV%2BDd24pZuz57BuJ7nu2wlv7pow%3D%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'9680adaf7b8bba5f-DAC')])
2025-08-01 04:54:34,805 - httpx - INFO - HTTP Request: POST https://decorupbeat.com/wp-json/wp/v2/posts/179 "HTTP/1.1 200 OK"
2025-08-01 04:54:34,805 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-01 04:54:34,809 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-01 04:54:34,809 - httpcore.http11 - DEBUG - response_closed.started
2025-08-01 04:54:34,810 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-01 04:54:34,810 - httpcore.connection - DEBUG - close.started
2025-08-01 04:54:34,811 - httpcore.connection - DEBUG - close.complete
2025-08-01 04:54:34,811 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 179 on decorupbeat.com
2025-08-01 04:54:34,811 - amazon_affiliate_integration.clients.wordpress_client - INFO - Batch update completed on decorupbeat.com: 1 successful, 0 failed
2025-08-01 04:54:34,811 - amazon_affiliate_integration - INFO - WordPress updates completed: 1 successful, 0 failed
2025-08-01 04:54:34,813 - amazon_affiliate_integration.processors.state_manager - DEBUG - Successfully wrote D:\Amazon_Addon_For_Pinterest_Automation_v2\processed_urls.json
2025-08-01 04:54:34,814 - amazon_affiliate_integration - INFO - Updated state with 1 newly processed URLs
2025-08-01 04:54:35,074 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Post-processing checkpoint for decorupbeat.com (1 articles processed)
2025-08-01 04:54:35,074 - amazon_affiliate_integration - INFO - Domain processing completed for decorupbeat.com: 1 processed, 0 failed, 0 skipped in 20.37s
