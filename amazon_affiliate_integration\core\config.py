"""
Configuration management for Amazon Affiliate Integration

Centralized configuration for all system components including concurrency limits,
file paths, API settings, and shortcode templates.
"""

import os
from pathlib import Path
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables with interpolation
load_dotenv(interpolate=True)

# =============================================================================
# CONCURRENCY & PERFORMANCE SETTINGS
# =============================================================================

# Configurable concurrency limit for async processing
CONCURRENCY_LIMIT = 4

# Batch size for processing operations
BATCH_SIZE = 10

# Rate limiting settings
CRAWLER_DELAY_SECONDS = 1.0  # Delay between crawler requests
API_RATE_LIMIT_DELAY = 0.5   # Delay between API requests

# =============================================================================
# STATE MANAGEMENT FILES
# =============================================================================

STATE_FILES = {
    'processed_urls': 'processed_urls.json',
    'excluded_urls': 'excluded_urls.json',
    'missing_products': 'missing_products.json'
}

# =============================================================================
# API CONFIGURATION
# =============================================================================

# OpenAI Configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
OPENAI_MODEL = 'gpt-4.1-nano'
OPENAI_MAX_TOKENS = 150
OPENAI_TEMPERATURE = 0.7

# AAWP Configuration
AAWP_TRACKING_ID = os.getenv('AAWP_TRACKING_ID', 'piatto-20')

# =============================================================================
# BACKUP & LOGGING CONFIGURATION
# =============================================================================

# Backup directory
AFFILIATE_BACKUP_DIR = Path(os.getenv('AFFILIATE_BACKUP_DIR', 'amazon_affiliate_backups'))

# Logging configuration
LOG_DIR = Path('logs')
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# =============================================================================
# WORDPRESS CREDENTIALS
# =============================================================================

WP_CREDENTIALS = {
    'plumbingreads.com': os.getenv('PLUMBINGREADS_COM_WP_CREDENTIALS'),
    'majesticmoods.com': os.getenv('MAJESTICMOODS_COM_WP_CREDENTIALS'),
    'interiornook.com': os.getenv('INTERIORNOOK_COM_WP_CREDENTIALS'),
    'decorupbeat.com': os.getenv('DECORUPBEAT_COM_WP_CREDENTIALS'),
    'polisheddecor.com': os.getenv('POLISHEDDECOR_COM_WP_CREDENTIALS'),
    'lavisliving.com': os.getenv('LAVISLIVING_COM_WP_CREDENTIALS'),
    'cozytones.com': os.getenv('COZYTONES_COM_WP_CREDENTIALS'),
    'showerredefined.com': os.getenv('SHOWERREDEFINED_COM_WP_CREDENTIALS'),
    'modernistinterior.com': os.getenv('MODERNISTINTERIOR_COM_WP_CREDENTIALS'),
    'cozycues.com': os.getenv('COZYCUES_COM_WP_CREDENTIALS'),
}

# =============================================================================
# SHORTCODE TEMPLATES
# =============================================================================

# Heading template for product recommendations
PRODUCT_HEADING_TEMPLATE = '''<!-- wp:heading -->
<h3 class="wp-block-heading">Recommended Products to replicate this idea</h3>
<!-- /wp:heading -->'''

# Shortcode template for individual products
SHORTCODE_TEMPLATE = '''<!-- wp:shortcode -->
[amazon bestseller="{product}" filterby="price" filter="1000" filter_compare="less" items="1" template="table" tracking_id="{tracking_id}"]
<!-- /wp:shortcode -->'''

# Combined template for product section (heading + shortcode)
PRODUCT_SECTION_TEMPLATE = f'''{PRODUCT_HEADING_TEMPLATE}
{SHORTCODE_TEMPLATE}'''

# =============================================================================
# CRAWLER CONFIGURATION
# =============================================================================

# Regex pattern for detecting visible shortcodes
SHORTCODE_REGEX_PATTERN = r'\[amazon bestseller="([^"]+)"[^\]]*\]'

# HTTP request configuration
HTTP_TIMEOUT = 30.0
HTTP_USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'

# API timeout and retry settings
API_TIMEOUT = 30.0
API_RETRY_ATTEMPTS = 3

# =============================================================================
# VALIDATION & ERROR HANDLING
# =============================================================================

# Required environment variables
REQUIRED_ENV_VARS = [
    'OPENAI_API_KEY',
    'AAWP_TRACKING_ID'
]

# Supported domains (must have credentials)
SUPPORTED_DOMAINS = list(WP_CREDENTIALS.keys())

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def validate_configuration() -> Dict[str, Any]:
    """
    Validate configuration and return validation results
    
    Returns:
        Dict containing validation status and any errors
    """
    errors = []
    warnings = []
    
    # Check required environment variables
    for var in REQUIRED_ENV_VARS:
        if not os.getenv(var):
            errors.append(f"Missing required environment variable: {var}")
    
    # Check WordPress credentials
    configured_domains = [domain for domain, creds in WP_CREDENTIALS.items() if creds]
    if not configured_domains:
        errors.append("No WordPress credentials configured")
    else:
        missing_domains = [domain for domain, creds in WP_CREDENTIALS.items() if not creds]
        if missing_domains:
            warnings.append(f"Missing credentials for domains: {', '.join(missing_domains)}")
    
    # Check directory permissions
    try:
        AFFILIATE_BACKUP_DIR.mkdir(parents=True, exist_ok=True)
        LOG_DIR.mkdir(parents=True, exist_ok=True)
    except PermissionError as e:
        errors.append(f"Permission error creating directories: {e}")
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings,
        'configured_domains': configured_domains
    }

def get_domain_credentials(domain: str) -> str:
    """
    Get WordPress credentials for a specific domain
    
    Args:
        domain: Domain name
        
    Returns:
        Credentials string or None if not configured
    """
    return WP_CREDENTIALS.get(domain)

def get_configured_domains() -> list:
    """
    Get list of domains with configured credentials

    Returns:
        List of domain names with valid credentials
    """
    return [domain for domain, creds in WP_CREDENTIALS.items() if creds]

# =============================================================================
# CRAWLER CONFIGURATION
# =============================================================================

CRAWLER_CONFIG = {
    'concurrency_limit': 4,      # Max concurrent HTTP requests
    'request_delay': 0.5,        # Delay between requests (seconds)
    'timeout': 30,               # Request timeout (seconds)
    'max_retries': 3,            # Max retry attempts per request
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

# Browser crawler configuration
BROWSER_CRAWLER_CONFIG = {
    'concurrency_limit': 2,      # Max concurrent browser pages (lower than HTTP)
    'request_delay': 1.0,        # Delay between page loads (seconds)
    'timeout': 45,               # Page load timeout (seconds)
    'max_retries': 2,            # Max retry attempts per page
    'headless': True,            # Run browser in headless mode
    'viewport_width': 1920,      # Browser viewport width
    'viewport_height': 1080,     # Browser viewport height
}
