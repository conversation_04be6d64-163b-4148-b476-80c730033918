2025-07-31 16:44:36,406 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250731_164436.log
2025-07-31 16:44:36,407 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250731_164436.log
2025-07-31 16:44:36,408 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-07-31 16:44:36,408 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-07-31 16:44:36,408 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-07-31 16:44:36,409 - __main__ - INFO - Starting complete workflow: Process → Crawl → Cleanup...
2025-07-31 16:44:36,409 - amazon_affiliate_integration - INFO - 🚀 Starting COMPLETE workflow: Process → Crawl → Cleanup
2025-07-31 16:44:36,410 - amazon_affiliate_integration - INFO - 📝 Phase 1: Processing articles to add Amazon shortcodes...
2025-07-31 16:44:36,410 - amazon_affiliate_integration - ERROR - ❌ Complete workflow failed: 'EnhancedOrchestrator' object has no attribute 'process_domain_articles'
2025-07-31 16:44:36,410 - __main__ - ERROR - Complete workflow failed
