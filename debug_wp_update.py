#!/usr/bin/env python3
"""
Debug script to test WordPress API update functionality
"""

import asyncio
import sys
import os
import httpx

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from amazon_affiliate_integration.clients.wordpress_client import WordPressClient
from amazon_affiliate_integration.core.config import WP_CREDENTIALS

async def test_wp_update(domain: str, article_id: int):
    """Test WordPress API update with minimal content change"""
    
    if domain not in WP_CREDENTIALS:
        print(f"❌ No credentials found for domain: {domain}")
        return
    
    try:
        # Initialize WordPress client
        wp_client = WordPressClient(domain, WP_CREDENTIALS[domain])
        
        print(f"🔍 Testing WordPress API update for article {article_id} on {domain}...")
        
        # Fetch current article
        article = await wp_client.get_article_by_id(article_id)
        
        if not article:
            print(f"❌ Article {article_id} not found")
            return
        
        print(f"✅ Article found: {article['title']['rendered']}")
        
        # Get current content
        current_content = article['content']['raw']
        print(f"📝 Current content length: {len(current_content)} characters")
        
        # Make a minimal change - add a comment at the end
        test_comment = "<!-- Test update by debug script -->"
        
        if test_comment in current_content:
            # Remove the test comment if it exists
            new_content = current_content.replace(test_comment, "")
            print("🔄 Removing test comment...")
        else:
            # Add the test comment
            new_content = current_content + "\n" + test_comment
            print("➕ Adding test comment...")
        
        print(f"📝 New content length: {len(new_content)} characters")
        
        # Test the update using the WordPress client
        print("🚀 Attempting WordPress API update...")
        
        # Get credentials for direct API call
        credentials = WP_CREDENTIALS[domain]
        parts = credentials.split('|')
        site_url = parts[0].rstrip('/')
        username = parts[1]
        password = parts[2]
        
        # Make direct API call to see full response
        auth = httpx.BasicAuth(username, password)
        
        async with httpx.AsyncClient(auth=auth, timeout=30.0) as client:
            payload = {'content': new_content}
            
            print(f"📡 Making POST request to: {site_url}/wp-json/wp/v2/posts/{article_id}")
            print(f"📦 Payload size: {len(str(payload))} characters")
            
            response = await client.post(
                f"{site_url}/wp-json/wp/v2/posts/{article_id}",
                json=payload
            )
            
            print(f"📊 Response status: {response.status_code}")
            print(f"📋 Response headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ API call successful!")
                print(f"📝 Response content length: {len(response_data.get('content', {}).get('raw', ''))}")
                print(f"📅 Response modified: {response_data.get('modified', 'N/A')}")
                
                # Check if our test comment is in the response
                response_content = response_data.get('content', {}).get('raw', '')
                if test_comment in response_content:
                    print("✅ Test comment found in API response!")
                else:
                    print("❌ Test comment NOT found in API response!")
                
            else:
                print(f"❌ API call failed: {response.status_code}")
                print(f"📄 Response text: {response.text}")
        
        # Wait a moment and fetch the article again to verify
        print("\n⏳ Waiting 2 seconds and fetching article again...")
        await asyncio.sleep(2)
        
        updated_article = await wp_client.get_article_by_id(article_id)
        if updated_article:
            updated_content = updated_article['content']['raw']
            print(f"📝 Updated content length: {len(updated_content)} characters")
            print(f"📅 Updated modified: {updated_article['modified']}")
            
            if test_comment in updated_content:
                print("✅ Test comment found in fetched content - UPDATE SUCCESSFUL!")
            else:
                print("❌ Test comment NOT found in fetched content - UPDATE FAILED!")
                
                # Show content differences
                if len(updated_content) != len(current_content):
                    print(f"📊 Content length changed: {len(current_content)} → {len(updated_content)}")
                else:
                    print("📊 Content length unchanged")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main function"""
    if len(sys.argv) != 3:
        print("Usage: python debug_wp_update.py <domain> <article_id>")
        print("Example: python debug_wp_update.py decorupbeat.com 179")
        sys.exit(1)
    
    domain = sys.argv[1]
    article_id = int(sys.argv[2])
    
    await test_wp_update(domain, article_id)

if __name__ == "__main__":
    asyncio.run(main())
