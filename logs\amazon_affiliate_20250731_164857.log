2025-07-31 16:48:57,288 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250731_164857.log
2025-07-31 16:48:57,290 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250731_164857.log
2025-07-31 16:48:57,290 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-07-31 16:48:57,291 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-07-31 16:48:57,291 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-07-31 16:48:57,292 - __main__ - INFO - Starting complete workflow: Process → Crawl → Cleanup...
2025-07-31 16:48:57,292 - amazon_affiliate_integration - INFO - 🚀 Starting COMPLETE workflow: Process → Crawl → Cleanup
2025-07-31 16:48:57,292 - amazon_affiliate_integration - INFO - 📝 Phase 1: Processing articles to add Amazon shortcodes...
2025-07-31 16:48:57,292 - amazon_affiliate_integration - INFO - Starting domain processing: decorupbeat.com (force=False, dry_run=False, limit=5)
2025-07-31 16:48:57,292 - amazon_affiliate_integration - INFO - Loaded 0 previously processed URLs
2025-07-31 16:48:57,293 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-07-31 16:48:57,293 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for decorupbeat.com (https://decorupbeat.com)
2025-07-31 16:48:59,473 - httpx - INFO - HTTP Request: GET https://decorupbeat.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-07-31 16:48:59,477 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for decorupbeat.com
2025-07-31 16:48:59,477 - amazon_affiliate_integration - INFO - Fetching articles from decorupbeat.com...
2025-07-31 16:48:59,477 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from decorupbeat.com since 2025-01-01T00:00:00
2025-07-31 16:49:02,677 - httpx - INFO - HTTP Request: GET https://decorupbeat.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-07-31 16:49:03,800 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 76 articles from page 1
2025-07-31 16:49:03,800 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 5 articles for testing
2025-07-31 16:49:03,800 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from decorupbeat.com: 5
2025-07-31 16:49:03,802 - amazon_affiliate_integration.clients.wordpress_client - ERROR - Error converting article 179 to ArticleInfo: ArticleInfo.__init__() got an unexpected keyword argument 'date_published'
2025-07-31 16:49:03,803 - amazon_affiliate_integration.clients.wordpress_client - ERROR - Error converting article 178 to ArticleInfo: ArticleInfo.__init__() got an unexpected keyword argument 'date_published'
2025-07-31 16:49:03,803 - amazon_affiliate_integration.clients.wordpress_client - ERROR - Error converting article 177 to ArticleInfo: ArticleInfo.__init__() got an unexpected keyword argument 'date_published'
2025-07-31 16:49:03,803 - amazon_affiliate_integration.clients.wordpress_client - ERROR - Error converting article 176 to ArticleInfo: ArticleInfo.__init__() got an unexpected keyword argument 'date_published'
2025-07-31 16:49:03,803 - amazon_affiliate_integration.clients.wordpress_client - ERROR - Error converting article 175 to ArticleInfo: ArticleInfo.__init__() got an unexpected keyword argument 'date_published'
2025-07-31 16:49:03,803 - amazon_affiliate_integration - INFO - Converted 0 articles to ArticleInfo objects
2025-07-31 16:49:03,804 - amazon_affiliate_integration - ERROR - Error processing domain decorupbeat.com: 'ProcessingState' object has no attribute 'force_mode'
2025-07-31 16:49:03,804 - amazon_affiliate_integration - ERROR - ❌ Phase 1 failed - aborting complete workflow
2025-07-31 16:49:03,804 - __main__ - ERROR - Complete workflow failed
