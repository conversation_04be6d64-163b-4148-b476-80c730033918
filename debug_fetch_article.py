#!/usr/bin/env python3
"""
Debug script to fetch current article content from WordPress
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from amazon_affiliate_integration.clients.wordpress_client import WordPressClient
from amazon_affiliate_integration.core.config import WP_CREDENTIALS

async def fetch_article_content(domain: str, article_id: int):
    """Fetch and display current article content"""
    
    if domain not in WP_CREDENTIALS:
        print(f"❌ No credentials found for domain: {domain}")
        return
    
    try:
        # Initialize WordPress client
        wp_client = WordPressClient(domain, WP_CREDENTIALS[domain])
        
        print(f"🔍 Fetching article {article_id} from {domain}...")
        
        # Fetch article
        article = await wp_client.get_article_by_id(article_id)
        
        if not article:
            print(f"❌ Article {article_id} not found")
            return
        
        print(f"✅ Article found: {article['title']['rendered']}")
        print(f"📝 Content length: {len(article['content']['raw'])} characters")
        print(f"🔗 URL: {article['link']}")
        print(f"📅 Modified: {article['modified']}")
        
        # Check for shortcodes in content
        content = article['content']['raw']
        
        # Count Amazon shortcodes
        import re
        shortcode_pattern = re.compile(r'\[amazon bestseller="([^"]+)"[^\]]*\]', re.IGNORECASE | re.MULTILINE)
        shortcodes = shortcode_pattern.findall(content)
        
        print(f"\n🔍 Amazon shortcodes found: {len(shortcodes)}")
        
        if shortcodes:
            print("\n📋 Shortcodes detected:")
            for i, shortcode in enumerate(shortcodes[:10], 1):  # Show first 10
                print(f"  {i}. [amazon bestseller=\"{shortcode}\" ...]")
            if len(shortcodes) > 10:
                print(f"  ... and {len(shortcodes) - 10} more")
        else:
            print("❌ No Amazon shortcodes found in content")
        
        # Show first 500 characters of content
        print(f"\n📄 Content preview (first 500 chars):")
        print("-" * 50)
        print(content[:500])
        if len(content) > 500:
            print("...")
        print("-" * 50)
        
    except Exception as e:
        print(f"❌ Error fetching article: {e}")

async def main():
    """Main function"""
    if len(sys.argv) != 3:
        print("Usage: python debug_fetch_article.py <domain> <article_id>")
        print("Example: python debug_fetch_article.py decorupbeat.com 179")
        sys.exit(1)
    
    domain = sys.argv[1]
    article_id = int(sys.argv[2])
    
    await fetch_article_content(domain, article_id)

if __name__ == "__main__":
    asyncio.run(main())
