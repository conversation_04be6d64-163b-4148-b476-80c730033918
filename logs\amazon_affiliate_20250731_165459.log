2025-07-31 16:54:59,690 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250731_165459.log
2025-07-31 16:54:59,691 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250731_165459.log
2025-07-31 16:54:59,691 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-07-31 16:54:59,692 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-07-31 16:54:59,692 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-07-31 16:54:59,702 - __main__ - INFO - Starting complete workflow: Process → Crawl → Cleanup...
2025-07-31 16:54:59,702 - amazon_affiliate_integration - INFO - 🚀 Starting COMPLETE workflow: Process → Crawl → Cleanup
2025-07-31 16:54:59,702 - amazon_affiliate_integration - INFO - 📝 Phase 1: Processing articles to add Amazon shortcodes...
2025-07-31 16:54:59,702 - amazon_affiliate_integration - INFO - Starting domain processing: decorupbeat.com (force=False, dry_run=False, limit=3)
2025-07-31 16:54:59,711 - amazon_affiliate_integration - INFO - Loaded 5 previously processed URLs
2025-07-31 16:54:59,712 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-07-31 16:54:59,712 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for decorupbeat.com (https://decorupbeat.com)
2025-07-31 16:55:01,417 - httpx - INFO - HTTP Request: GET https://decorupbeat.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-07-31 16:55:01,418 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for decorupbeat.com
2025-07-31 16:55:01,418 - amazon_affiliate_integration - INFO - Fetching articles from decorupbeat.com...
2025-07-31 16:55:01,419 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from decorupbeat.com since 2025-01-01T00:00:00
2025-07-31 16:55:04,243 - httpx - INFO - HTTP Request: GET https://decorupbeat.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-07-31 16:55:05,316 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 76 articles from page 1
2025-07-31 16:55:05,316 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 3 articles for testing
2025-07-31 16:55:05,316 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from decorupbeat.com: 3
2025-07-31 16:55:05,318 - amazon_affiliate_integration - INFO - Converted 3 articles to ArticleInfo objects
2025-07-31 16:55:05,319 - amazon_affiliate_integration - INFO - Normal mode: processing 0 articles (skipping 3 processed, 0 excluded)
2025-07-31 16:55:05,319 - amazon_affiliate_integration - INFO - No articles to process for decorupbeat.com after filtering
2025-07-31 16:55:05,319 - amazon_affiliate_integration - INFO - ✅ Phase 1 completed successfully
2025-07-31 16:55:05,319 - amazon_affiliate_integration - INFO - 🕷️ Phase 2 & 3: Crawling and cleaning up failed shortcodes...
2025-07-31 16:55:05,320 - amazon_affiliate_integration - INFO - Starting crawl and cleanup workflow...
2025-07-31 16:55:05,320 - amazon_affiliate_integration - INFO - Phase 1: Crawling articles for visible shortcodes...
2025-07-31 16:55:05,322 - amazon_affiliate_integration.crawlers.shortcode_crawler - INFO - Initialized crawler with 4 concurrent connections
2025-07-31 16:55:05,322 - amazon_affiliate_integration.crawlers.shortcode_crawler - INFO - Starting to crawl 5 articles for visible shortcodes
2025-07-31 16:55:07,962 - amazon_affiliate_integration.crawlers.shortcode_crawler - INFO - Crawled 5/5 articles (0 visible shortcodes found)
2025-07-31 16:55:07,963 - amazon_affiliate_integration.crawlers.shortcode_crawler - INFO - Crawling completed: {'total_crawled': 5, 'successful_crawls': 5, 'failed_crawls': 0, 'visible_shortcodes_found': 0, 'total_shortcodes_detected': 0}
2025-07-31 16:55:07,964 - amazon_affiliate_integration - INFO - Crawled 5 articles for domain decorupbeat.com, found 0 visible shortcodes
2025-07-31 16:55:07,964 - amazon_affiliate_integration - INFO - Phase 2: Cleaning up failed shortcodes...
2025-07-31 16:55:07,964 - amazon_affiliate_integration - ERROR - Crawl and cleanup workflow failed: 'dict' object has no attribute 'success'
2025-07-31 16:55:07,964 - amazon_affiliate_integration - WARNING - ⚠️ Phase 2 & 3 had issues, but processing was successful
2025-07-31 16:55:07,964 - amazon_affiliate_integration - INFO - 🎉 COMPLETE workflow finished successfully in 8.26s
2025-07-31 16:55:07,965 - __main__ - INFO - Complete workflow finished successfully
