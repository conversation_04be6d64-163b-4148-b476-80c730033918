"""
Backup Manager for Amazon Affiliate Integration

Enhanced backup system that ensures Gutenberg block format preservation,
creates automatic git checkpoints, and provides comprehensive restore capabilities.
"""

import json
import logging
import asyncio
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import aiofiles
import re

from ..core.config import AFFILIATE_BACKUP_DIR
from ..core.models import ArticleInfo

class BackupManager:
    """
    Manages backup operations for WordPress content with Gutenberg block preservation
    
    Features:
    - Gutenberg block format preservation
    - Automatic git checkpoint creation
    - Atomic backup operations
    - Comprehensive restore capabilities
    - Backup validation and integrity checks
    """
    
    def __init__(self, backup_dir: Path = None):
        """
        Initialize BackupManager
        
        Args:
            backup_dir: Directory to store backups (default: from config)
        """
        self.backup_dir = backup_dir or AFFILIATE_BACKUP_DIR
        self.logger = logging.getLogger(__name__)
        
        # Ensure backup directory exists
        self.backup_dir.mkdir(parents=True, exist_ok=True)
    
    def _convert_html_to_gutenberg(self, html_content: str) -> str:
        """
        Convert raw HTML content to proper Gutenberg block format
        
        Args:
            html_content: Raw HTML content
            
        Returns:
            Content formatted as Gutenberg blocks
        """
        # If content is already in Gutenberg format, return as-is
        if '<!-- wp:' in html_content:
            return html_content
        
        # Convert common HTML elements to Gutenberg blocks
        content = html_content
        
        # Convert headings
        content = re.sub(
            r'<h2([^>]*)>(.*?)</h2>',
            r'<!-- wp:heading {"level":2} -->\n<h2 class="wp-block-heading"\1>\2</h2>\n<!-- /wp:heading -->',
            content,
            flags=re.DOTALL
        )
        
        content = re.sub(
            r'<h3([^>]*)>(.*?)</h3>',
            r'<!-- wp:heading {"level":3} -->\n<h3 class="wp-block-heading"\1>\2</h3>\n<!-- /wp:heading -->',
            content,
            flags=re.DOTALL
        )
        
        content = re.sub(
            r'<h4([^>]*)>(.*?)</h4>',
            r'<!-- wp:heading {"level":4} -->\n<h4 class="wp-block-heading"\1>\2</h4>\n<!-- /wp:heading -->',
            content,
            flags=re.DOTALL
        )
        
        # Convert paragraphs
        content = re.sub(
            r'<p([^>]*)>(.*?)</p>',
            r'<!-- wp:paragraph -->\n<p\1>\2</p>\n<!-- /wp:paragraph -->',
            content,
            flags=re.DOTALL
        )
        
        # Convert lists
        content = re.sub(
            r'<ul([^>]*)>(.*?)</ul>',
            r'<!-- wp:list -->\n<ul\1>\2</ul>\n<!-- /wp:list -->',
            content,
            flags=re.DOTALL
        )
        
        content = re.sub(
            r'<ol([^>]*)>(.*?)</ol>',
            r'<!-- wp:list {"ordered":true} -->\n<ol\1>\2</ol>\n<!-- /wp:list -->',
            content,
            flags=re.DOTALL
        )
        
        # Convert images
        content = re.sub(
            r'<img([^>]*?)>',
            r'<!-- wp:image -->\n<figure class="wp-block-image"><img\1></figure>\n<!-- /wp:image -->',
            content
        )
        
        # Wrap any remaining content in paragraph blocks
        lines = content.split('\n')
        processed_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Skip if already a Gutenberg block
            if line.startswith('<!-- wp:') or line.startswith('<!-- /wp:'):
                processed_lines.append(line)
                continue
            
            # Skip if it's HTML that's part of a block
            if any(tag in line for tag in ['<h2', '<h3', '<h4', '<p', '<ul', '<ol', '<img', '<figure']):
                processed_lines.append(line)
                continue
            
            # Wrap plain text in paragraph block
            if line and not line.startswith('<'):
                processed_lines.extend([
                    '<!-- wp:paragraph -->',
                    f'<p>{line}</p>',
                    '<!-- /wp:paragraph -->'
                ])
            else:
                processed_lines.append(line)
        
        return '\n'.join(processed_lines)
    
    async def create_backup(self, article: ArticleInfo, backup_type: str = "pre_processing") -> Optional[str]:
        """
        Create a backup of article content in Gutenberg block format
        
        Args:
            article: Article information
            backup_type: Type of backup (pre_processing, post_processing, etc.)
            
        Returns:
            Backup filename if successful, None otherwise
        """
        try:
            # Generate backup filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{article.domain}_{article.id}_{backup_type}_{timestamp}.json"
            backup_path = self.backup_dir / backup_filename
            
            # Convert content to Gutenberg format
            gutenberg_content = self._convert_html_to_gutenberg(article.content)
            
            # Create backup data
            backup_data = {
                'article_id': article.id,
                'title': article.title,
                'url': article.url,
                'domain': article.domain,
                'slug': article.slug,
                'original_content': article.content,
                'gutenberg_content': gutenberg_content,
                'date': article.date,
                'modified': article.modified,
                'backup_type': backup_type,
                'backup_timestamp': timestamp,
                'backup_date': datetime.now().isoformat(),
                'content_length': len(article.content),
                'gutenberg_blocks_count': gutenberg_content.count('<!-- wp:')
            }
            
            # Write backup file atomically
            temp_path = backup_path.with_suffix('.tmp')
            async with aiofiles.open(temp_path, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(backup_data, indent=2, ensure_ascii=False))
            
            # Atomic move
            await asyncio.to_thread(temp_path.rename, backup_path)
            
            self.logger.info(f"Created backup: {backup_filename}")
            return backup_filename
            
        except Exception as e:
            self.logger.error(f"Error creating backup for article {article.id}: {e}")
            # Clean up temp file if it exists
            if 'temp_path' in locals() and temp_path.exists():
                try:
                    await asyncio.to_thread(temp_path.unlink)
                except:
                    pass
            return None
    
    async def create_git_checkpoint(self, message: str = None) -> bool:
        """
        Create automatic git checkpoint for rollback capabilities
        
        Args:
            message: Commit message (auto-generated if None)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if message is None:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                message = f"Amazon Affiliate Integration checkpoint - {timestamp}"
            
            # Check if we're in a git repository
            result = await asyncio.to_thread(
                subprocess.run,
                ['git', 'rev-parse', '--git-dir'],
                capture_output=True,
                text=True,
                cwd=self.backup_dir.parent
            )
            
            if result.returncode != 0:
                self.logger.warning("Not in a git repository. Skipping git checkpoint.")
                return False
            
            # Add all changes
            await asyncio.to_thread(
                subprocess.run,
                ['git', 'add', '.'],
                cwd=self.backup_dir.parent,
                check=True
            )
            
            # Check if there are changes to commit
            result = await asyncio.to_thread(
                subprocess.run,
                ['git', 'diff', '--cached', '--quiet'],
                cwd=self.backup_dir.parent
            )
            
            if result.returncode == 0:
                self.logger.info("No changes to commit for git checkpoint.")
                return True
            
            # Create commit
            await asyncio.to_thread(
                subprocess.run,
                ['git', 'commit', '-m', message],
                cwd=self.backup_dir.parent,
                check=True
            )
            
            self.logger.info(f"Created git checkpoint: {message}")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Git command failed: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error creating git checkpoint: {e}")
            return False
    
    async def load_backup(self, backup_filename: str) -> Optional[Dict[str, Any]]:
        """
        Load backup data from file
        
        Args:
            backup_filename: Name of backup file
            
        Returns:
            Backup data dictionary or None if error
        """
        try:
            backup_path = self.backup_dir / backup_filename
            
            if not backup_path.exists():
                self.logger.error(f"Backup file not found: {backup_filename}")
                return None
            
            async with aiofiles.open(backup_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                return json.loads(content)
                
        except Exception as e:
            self.logger.error(f"Error loading backup {backup_filename}: {e}")
            return None
    
    async def list_backups(self, domain: str = None, article_id: int = None) -> List[Dict[str, Any]]:
        """
        List available backups with filtering options
        
        Args:
            domain: Filter by domain
            article_id: Filter by article ID
            
        Returns:
            List of backup information dictionaries
        """
        try:
            backups = []
            
            for backup_file in self.backup_dir.glob("*.json"):
                try:
                    backup_data = await self.load_backup(backup_file.name)
                    if not backup_data:
                        continue
                    
                    # Apply filters
                    if domain and backup_data.get('domain') != domain:
                        continue
                    
                    if article_id and backup_data.get('article_id') != article_id:
                        continue
                    
                    # Add file information
                    backup_info = {
                        'filename': backup_file.name,
                        'article_id': backup_data.get('article_id'),
                        'title': backup_data.get('title'),
                        'domain': backup_data.get('domain'),
                        'backup_type': backup_data.get('backup_type'),
                        'backup_date': backup_data.get('backup_date'),
                        'content_length': backup_data.get('content_length'),
                        'file_size': backup_file.stat().st_size
                    }
                    
                    backups.append(backup_info)
                    
                except Exception as e:
                    self.logger.warning(f"Error processing backup file {backup_file.name}: {e}")
                    continue
            
            # Sort by backup date (newest first)
            backups.sort(key=lambda x: x.get('backup_date', ''), reverse=True)
            return backups
            
        except Exception as e:
            self.logger.error(f"Error listing backups: {e}")
            return []
    
    async def validate_backup(self, backup_filename: str) -> Dict[str, Any]:
        """
        Validate backup file integrity and content
        
        Args:
            backup_filename: Name of backup file to validate
            
        Returns:
            Validation results dictionary
        """
        try:
            backup_data = await self.load_backup(backup_filename)
            
            if not backup_data:
                return {'valid': False, 'errors': ['Could not load backup file']}
            
            errors = []
            warnings = []
            
            # Check required fields
            required_fields = ['article_id', 'title', 'url', 'domain', 'original_content', 'gutenberg_content']
            for field in required_fields:
                if field not in backup_data:
                    errors.append(f"Missing required field: {field}")
            
            # Validate content
            if 'original_content' in backup_data and 'gutenberg_content' in backup_data:
                original_len = len(backup_data['original_content'])
                gutenberg_len = len(backup_data['gutenberg_content'])
                
                if gutenberg_len < original_len * 0.8:  # Gutenberg should not be significantly shorter
                    warnings.append("Gutenberg content is significantly shorter than original")
                
                # Check if Gutenberg format is properly applied
                gutenberg_content = backup_data['gutenberg_content']
                if '<!-- wp:' not in gutenberg_content:
                    warnings.append("Content may not be properly converted to Gutenberg format")
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'backup_date': backup_data.get('backup_date'),
                'content_stats': {
                    'original_length': len(backup_data.get('original_content', '')),
                    'gutenberg_length': len(backup_data.get('gutenberg_content', '')),
                    'gutenberg_blocks': backup_data.get('gutenberg_blocks_count', 0)
                }
            }
            
        except Exception as e:
            return {
                'valid': False,
                'errors': [f"Validation error: {str(e)}"],
                'warnings': []
            }
    
    async def cleanup_old_backups(self, days_to_keep: int = 30) -> int:
        """
        Clean up old backup files
        
        Args:
            days_to_keep: Number of days to keep backups
            
        Returns:
            Number of files cleaned up
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
            cleaned_count = 0
            
            for backup_file in self.backup_dir.glob("*.json"):
                if backup_file.stat().st_mtime < cutoff_time:
                    try:
                        await asyncio.to_thread(backup_file.unlink)
                        cleaned_count += 1
                        self.logger.debug(f"Cleaned up old backup: {backup_file.name}")
                    except Exception as e:
                        self.logger.warning(f"Error cleaning up {backup_file.name}: {e}")
            
            if cleaned_count > 0:
                self.logger.info(f"Cleaned up {cleaned_count} old backup files")
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Error during backup cleanup: {e}")
            return 0
